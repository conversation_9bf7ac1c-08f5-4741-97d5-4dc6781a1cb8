import { onRequest } from "firebase-functions/v2/https";
import { logger } from "firebase-functions";
import Strip<PERSON> from "stripe";
import { getFirestore } from "firebase-admin/firestore";

// Initialize Firestore
const db = getFirestore();

// Initialize Stripe with secret key
const stripe = new Stripe(
  "sk_test_51RW6bV2Z0fYxSFF6X7FkXK3FXaQO41XSLEB13ZLAQJa7IV5AxWEiO799U41ZnAN4CHNZlcI28yXD42KqFWkp0zcy00KoJ5GQeV",
  {
    apiVersion: "2025-05-28.basil",
  }
);

// CORS headers for local development
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization",
};

// Create Stripe Checkout Session
export const createCheckoutSession = onRequest(
  { cors: true },
  async (req, res) => {
    // Handle preflight requests
    if (req.method === "OPTIONS") {
      res.set(corsHeaders);
      res.status(200).send("");
      return;
    }

    // Only allow POST requests
    if (req.method !== "POST") {
      res.set(corsHeaders);
      res.status(405).json({ error: "Method not allowed" });
      return;
    }

    try {
      const { priceId, quantity, tenantId, customerEmail } = req.body;

      logger.info("Creating checkout session", {
        priceId,
        quantity,
        tenantId,
        customerEmail,
      });

      // Validate required fields
      if (!priceId || !quantity || !tenantId || !customerEmail) {
        res.set(corsHeaders);
        res.status(400).json({
          error:
            "Missing required fields: priceId, quantity, tenantId, customerEmail",
        });
        return;
      }

      // Create Stripe checkout session
      const session = await stripe.checkout.sessions.create({
        payment_method_types: ["card"],
        line_items: [
          {
            price: priceId,
            quantity: quantity,
          },
        ],
        mode: "subscription",
        success_url: `${
          req.headers.origin || "http://localhost:3000"
        }/dashboard?session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: `${req.headers.origin || "http://localhost:3000"}/billing`,
        customer_email: customerEmail,
        metadata: {
          tenantId: tenantId,
          locationCount: quantity.toString(),
        },
        subscription_data: {
          metadata: {
            tenantId: tenantId,
            locationCount: quantity.toString(),
          },
        },
        allow_promotion_codes: true,
      });

      logger.info("Checkout session created", { sessionId: session.id });

      res.set(corsHeaders);
      res.status(200).json({
        sessionId: session.id,
        url: session.url,
      });
    } catch (error) {
      logger.error("Error creating checkout session", error);
      res.set(corsHeaders);
      res.status(500).json({
        error: "Failed to create checkout session",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }
);

// Handle Stripe Webhooks
export const stripeWebhook = onRequest({ cors: true }, async (req, res) => {
  // Handle preflight requests
  if (req.method === "OPTIONS") {
    res.set(corsHeaders);
    res.status(200).send("");
    return;
  }

  // Only allow POST requests
  if (req.method !== "POST") {
    res.set(corsHeaders);
    res.status(405).json({ error: "Method not allowed" });
    return;
  }

  const sig = req.headers["stripe-signature"] as string;
  const endpointSecret = "whsec_your_webhook_secret_here"; // We'll get this from Stripe

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
  } catch (err) {
    logger.error("Webhook signature verification failed", err);
    res
      .status(400)
      .send(
        `Webhook Error: ${err instanceof Error ? err.message : "Unknown error"}`
      );
    return;
  }

  logger.info("Received webhook event", { type: event.type, id: event.id });

  try {
    switch (event.type) {
      case "customer.subscription.created":
        await handleSubscriptionCreated(
          event.data.object as Stripe.Subscription
        );
        break;
      case "customer.subscription.updated":
        await handleSubscriptionUpdated(
          event.data.object as Stripe.Subscription
        );
        break;
      case "customer.subscription.deleted":
        await handleSubscriptionDeleted(
          event.data.object as Stripe.Subscription
        );
        break;
      case "invoice.payment_succeeded":
        await handlePaymentSucceeded(event.data.object as Stripe.Invoice);
        break;
      case "invoice.payment_failed":
        await handlePaymentFailed(event.data.object as Stripe.Invoice);
        break;
      default:
        logger.info("Unhandled event type", { type: event.type });
    }

    res.status(200).json({ received: true });
  } catch (error) {
    logger.error("Error processing webhook", error);
    res.status(500).json({ error: "Webhook processing failed" });
  }
});

// Subscription management functions
interface SubscriptionData {
  subscriptionId: string;
  status: "active" | "canceled" | "past_due" | "unpaid" | "incomplete";
  currentPeriodEnd: string;
  locationCount: number;
  stripeCustomerId?: string;
}

async function updateTenantSubscription(
  tenantId: string,
  subscriptionData: SubscriptionData
): Promise<void> {
  try {
    logger.info("Updating tenant subscription", { tenantId, subscriptionData });

    const tenantRef = db.collection("tenants").doc(tenantId);

    const updateData = {
      subscriptionId: subscriptionData.subscriptionId,
      subscriptionStatus: subscriptionData.status,
      subscriptionCurrentPeriodEnd: subscriptionData.currentPeriodEnd,
      subscriptionLocationCount: subscriptionData.locationCount,
      lastPaymentDate:
        subscriptionData.status === "active"
          ? new Date().toISOString()
          : undefined,
      updatedAt: new Date().toISOString(),
    };

    // Remove undefined values
    Object.keys(updateData).forEach((key) => {
      if (updateData[key as keyof typeof updateData] === undefined) {
        delete updateData[key as keyof typeof updateData];
      }
    });

    await tenantRef.update(updateData);
    logger.info("Tenant subscription updated successfully", { tenantId });
  } catch (error) {
    logger.error("Error updating tenant subscription", { tenantId, error });
    throw error;
  }
}

async function updateUserSubscriptionStatus(
  tenantId: string,
  hasActiveSubscription: boolean
): Promise<void> {
  try {
    logger.info("Updating user subscription status", {
      tenantId,
      hasActiveSubscription,
    });

    // Find user profile by tenantId
    const userProfilesRef = db.collection("user_profiles");
    const query = userProfilesRef.where("tenantId", "==", tenantId);
    const snapshot = await query.get();

    if (snapshot.empty) {
      logger.warn("No user profiles found for tenant", { tenantId });
      return;
    }

    // Update all user profiles for this tenant
    const batch = db.batch();
    snapshot.docs.forEach((doc) => {
      batch.update(doc.ref, {
        hasActiveSubscription,
        lastLoginAt: new Date().toISOString(),
      });
    });

    await batch.commit();
    logger.info("User subscription status updated successfully", {
      tenantId,
      userCount: snapshot.docs.length,
    });
  } catch (error) {
    logger.error("Error updating user subscription status", {
      tenantId,
      error,
    });
    throw error;
  }
}

// Webhook handlers
async function handleSubscriptionCreated(subscription: Stripe.Subscription) {
  logger.info("Subscription created", {
    subscriptionId: subscription.id,
    tenantId: subscription.metadata.tenantId,
  });

  try {
    const tenantId = subscription.metadata.tenantId;
    const locationCount = parseInt(subscription.metadata.locationCount || "1");

    if (!tenantId) {
      logger.error("No tenantId in subscription metadata");
      return;
    }

    // Update tenant subscription info
    await updateTenantSubscription(tenantId, {
      subscriptionId: subscription.id,
      status: subscription.status as any,
      currentPeriodEnd: new Date(
        subscription.current_period_end * 1000
      ).toISOString(),
      locationCount,
      stripeCustomerId: subscription.customer as string,
    });

    // Update user profile subscription status
    await updateUserSubscriptionStatus(tenantId, true);

    logger.info("Subscription created successfully", {
      tenantId,
      subscriptionId: subscription.id,
    });
  } catch (error) {
    logger.error("Error handling subscription created", error);
  }
}

async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  logger.info("Subscription updated", {
    subscriptionId: subscription.id,
    status: subscription.status,
  });

  try {
    const tenantId = subscription.metadata.tenantId;
    const locationCount = parseInt(subscription.metadata.locationCount || "1");

    if (!tenantId) {
      logger.error("No tenantId in subscription metadata");
      return;
    }

    // Update tenant subscription info
    await updateTenantSubscription(tenantId, {
      subscriptionId: subscription.id,
      status: subscription.status as any,
      currentPeriodEnd: new Date(
        subscription.current_period_end * 1000
      ).toISOString(),
      locationCount,
      stripeCustomerId: subscription.customer as string,
    });

    // Update user profile subscription status
    const isActive = subscription.status === "active";
    await updateUserSubscriptionStatus(tenantId, isActive);

    logger.info("Subscription updated successfully", {
      tenantId,
      status: subscription.status,
    });
  } catch (error) {
    logger.error("Error handling subscription updated", error);
  }
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  logger.info("Subscription deleted", {
    subscriptionId: subscription.id,
  });

  try {
    const tenantId = subscription.metadata.tenantId;

    if (!tenantId) {
      logger.error("No tenantId in subscription metadata");
      return;
    }

    // Update tenant subscription info
    await updateTenantSubscription(tenantId, {
      subscriptionId: subscription.id,
      status: "canceled",
      currentPeriodEnd: new Date(
        subscription.current_period_end * 1000
      ).toISOString(),
      locationCount: parseInt(subscription.metadata.locationCount || "1"),
      stripeCustomerId: subscription.customer as string,
    });

    // Update user profile subscription status
    await updateUserSubscriptionStatus(tenantId, false);

    logger.info("Subscription deleted successfully", { tenantId });
  } catch (error) {
    logger.error("Error handling subscription deleted", error);
  }
}

async function handlePaymentSucceeded(invoice: Stripe.Invoice) {
  logger.info("Payment succeeded", {
    invoiceId: invoice.id,
    amount: invoice.amount_paid,
  });

  try {
    if (invoice.subscription) {
      // Get subscription details to update tenant
      const subscription = await stripe.subscriptions.retrieve(
        invoice.subscription as string
      );
      const tenantId = subscription.metadata.tenantId;

      if (tenantId) {
        // Update last payment date
        const tenantRef = db.collection("tenants").doc(tenantId);
        await tenantRef.update({
          lastPaymentDate: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        });

        logger.info("Payment recorded successfully", {
          tenantId,
          invoiceId: invoice.id,
        });
      }
    }
  } catch (error) {
    logger.error("Error handling payment succeeded", error);
  }
}

async function handlePaymentFailed(invoice: Stripe.Invoice) {
  logger.info("Payment failed", {
    invoiceId: invoice.id,
    amount: invoice.amount_due,
  });

  try {
    if (invoice.subscription) {
      // Get subscription details
      const subscription = await stripe.subscriptions.retrieve(
        invoice.subscription as string
      );
      const tenantId = subscription.metadata.tenantId;

      if (tenantId) {
        // Update subscription status to past_due if needed
        await updateTenantSubscription(tenantId, {
          subscriptionId: subscription.id,
          status: subscription.status as any,
          currentPeriodEnd: new Date(
            subscription.current_period_end * 1000
          ).toISOString(),
          locationCount: parseInt(subscription.metadata.locationCount || "1"),
          stripeCustomerId: subscription.customer as string,
        });

        // Update user subscription status
        const isActive = subscription.status === "active";
        await updateUserSubscriptionStatus(tenantId, isActive);

        logger.info("Payment failure handled", {
          tenantId,
          status: subscription.status,
        });
      }
    }
  } catch (error) {
    logger.error("Error handling payment failed", error);
  }
}
