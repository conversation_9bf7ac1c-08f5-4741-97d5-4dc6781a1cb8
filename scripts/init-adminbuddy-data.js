// Initialize AdminBuddy sample data in Firebase
const { initializeApp } = require('firebase/app');
const { getFirestore, collection, addDoc, doc, setDoc } = require('firebase/firestore');

// Firebase config - replace with your actual config
const firebaseConfig = {
  apiKey: "AIzaSyA-tPofXKa8nzrLXI12epnAwMS9H2pvaUs",
  authDomain: "bitbybit-courses.firebaseapp.com",
  projectId: "bitbybit-courses",
  storageBucket: "bitbybit-courses.appspot.com",
  messagingSenderId: "377798004351",
  appId: "1:377798004351:web:65d643d612b8aafb29b94a"
};

// Collection names
const COLLECTIONS = {
  LOCATIONS: "adminbuddy_locations",
  ROUTINES: "adminbuddy_routines", 
  TASKS: "adminbuddy_tasks",
  LOCATION_ROUTINES: "adminbuddy_location_routines",
};

async function initializeAdminBuddyData() {
  try {
    console.log('🔥 Initializing Firebase...');
    const app = initializeApp(firebaseConfig);
    const db = getFirestore(app);
    
    console.log('📍 Creating sample locations...');
    
    // Create locations
    const location1Ref = await addDoc(collection(db, COLLECTIONS.LOCATIONS), {
      name: "Downtown Coffee Shop",
      address: "123 Main St, Downtown",
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    const location2Ref = await addDoc(collection(db, COLLECTIONS.LOCATIONS), {
      name: "Westside Cafe",
      address: "456 Oak Ave, Westside", 
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    console.log('✅ Locations created:', location1Ref.id, location2Ref.id);
    
    console.log('🔄 Creating sample routines...');
    
    // Create routines
    const routine1Ref = await addDoc(collection(db, COLLECTIONS.ROUTINES), {
      name: "Opening",
      description: "Morning startup procedures",
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    const routine2Ref = await addDoc(collection(db, COLLECTIONS.ROUTINES), {
      name: "Closing",
      description: "End of day procedures", 
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    const routine3Ref = await addDoc(collection(db, COLLECTIONS.ROUTINES), {
      name: "Daily Maintenance",
      description: "Regular upkeep tasks",
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    const routine4Ref = await addDoc(collection(db, COLLECTIONS.ROUTINES), {
      name: "Hourly Cleaning",
      description: "Ongoing cleanliness tasks",
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    console.log('✅ Routines created');
    
    console.log('✅ Creating sample tasks...');
    
    // Create tasks
    await addDoc(collection(db, COLLECTIONS.TASKS), {
      title: "Unlock front door",
      description: "Turn off alarm, unlock main entrance",
      priority: "high",
      routineId: routine1Ref.id,
      estimatedMinutes: 2,
      requiredRole: "keyholder",
      dueTime: "08:00",
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    await addDoc(collection(db, COLLECTIONS.TASKS), {
      title: "Turn on coffee machines",
      description: "Start espresso machine, drip coffee, and grinder",
      priority: "high",
      routineId: routine1Ref.id,
      estimatedMinutes: 5,
      requiredRole: "any",
      dueTime: "08:00",
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    await addDoc(collection(db, COLLECTIONS.TASKS), {
      title: "Count register",
      description: "Count cash drawer, record amount",
      priority: "high",
      routineId: routine2Ref.id,
      estimatedMinutes: 5,
      requiredRole: "keyholder",
      dueTime: "20:00",
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    await addDoc(collection(db, COLLECTIONS.TASKS), {
      title: "Clean restrooms",
      description: "Check supplies, clean surfaces, restock paper",
      priority: "medium",
      routineId: routine3Ref.id,
      estimatedMinutes: 15,
      requiredRole: "any",
      dueTime: "10:00",
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    await addDoc(collection(db, COLLECTIONS.TASKS), {
      title: "Wipe down tables",
      description: "Clean all customer seating areas",
      priority: "low",
      routineId: routine4Ref.id,
      estimatedMinutes: 10,
      requiredRole: "any",
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    // Add some ad-hoc tasks (no routine)
    await addDoc(collection(db, COLLECTIONS.TASKS), {
      title: "Fix broken chair",
      description: "Repair the wobbly chair near the window",
      priority: "medium",
      estimatedMinutes: 20,
      requiredRole: "any",
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    await addDoc(collection(db, COLLECTIONS.TASKS), {
      title: "Order more coffee beans",
      description: "We're running low on Colombian blend",
      priority: "high",
      estimatedMinutes: 5,
      requiredRole: "manager",
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    console.log('✅ Tasks created');
    
    console.log('🔗 Creating location-routine relationships...');
    
    // Create location-routine relationships
    await addDoc(collection(db, COLLECTIONS.LOCATION_ROUTINES), {
      locationId: location1Ref.id,
      routineId: routine1Ref.id,
      isActive: true,
    });
    
    await addDoc(collection(db, COLLECTIONS.LOCATION_ROUTINES), {
      locationId: location1Ref.id,
      routineId: routine2Ref.id,
      isActive: true,
    });
    
    await addDoc(collection(db, COLLECTIONS.LOCATION_ROUTINES), {
      locationId: location1Ref.id,
      routineId: routine3Ref.id,
      isActive: true,
    });
    
    await addDoc(collection(db, COLLECTIONS.LOCATION_ROUTINES), {
      locationId: location1Ref.id,
      routineId: routine4Ref.id,
      isActive: true,
    });
    
    await addDoc(collection(db, COLLECTIONS.LOCATION_ROUTINES), {
      locationId: location2Ref.id,
      routineId: routine1Ref.id,
      isActive: true,
    });
    
    await addDoc(collection(db, COLLECTIONS.LOCATION_ROUTINES), {
      locationId: location2Ref.id,
      routineId: routine2Ref.id,
      isActive: true,
    });
    
    await addDoc(collection(db, COLLECTIONS.LOCATION_ROUTINES), {
      locationId: location2Ref.id,
      routineId: routine3Ref.id,
      isActive: true,
    });
    
    console.log('✅ Location-routine relationships created');
    
    console.log('🎉 AdminBuddy sample data initialization complete!');
    console.log('📊 Summary:');
    console.log('  - 2 Locations');
    console.log('  - 4 Routines');
    console.log('  - 7 Tasks (5 with routines, 2 ad-hoc)');
    console.log('  - 7 Location-routine relationships');
    
  } catch (error) {
    console.error('❌ Error initializing AdminBuddy data:', error);
    process.exit(1);
  }
}

// Run the initialization
if (require.main === module) {
  initializeAdminBuddyData();
}
