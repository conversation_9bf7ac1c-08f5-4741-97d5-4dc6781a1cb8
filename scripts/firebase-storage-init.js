// Simple script to test Firebase Storage initialization
const { initializeApp } = require('firebase/app');
const { getStorage, ref, connectStorageEmulator } = require('firebase/storage');

// Your actual Firebase config from .env.local
const firebaseConfig = {
  apiKey: "AIzaSyA-tPofXKa8nzrLXI12epnAwMS9H2pvaUs",
  authDomain: "bitbybit-courses.firebaseapp.com",
  projectId: "bitbybit-courses",
  storageBucket: "bitbybit-courses.appspot.com",
  messagingSenderId: "377798004351",
  appId: "1:377798004351:web:65d643d612b8aafb29b94a"
};

async function initializeFirebaseStorage() {
  try {
    console.log('🔥 Initializing Firebase Storage...');
    console.log('📁 Storage bucket:', firebaseConfig.storageBucket);
    
    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    console.log('✅ Firebase app initialized');
    
    // Get Storage instance
    const storage = getStorage(app);
    console.log('✅ Storage instance created');
    
    // Try to create a reference (this should work even if bucket doesn't exist)
    const testRef = ref(storage, 'test/init.txt');
    console.log('✅ Storage reference created:', testRef.fullPath);
    
    console.log('\n🎉 Firebase Storage initialization successful!');
    console.log('📊 Storage details:');
    console.log('   - Bucket:', storage.app.options.storageBucket);
    console.log('   - App name:', storage.app.name);
    
    // The bucket should be automatically created when we try to upload
    console.log('\n💡 Next steps:');
    console.log('1. Try deploying storage rules: firebase deploy --only storage:rules');
    console.log('2. Test image upload in the admin dashboard');
    console.log('3. The bucket will be created automatically on first upload');
    
    return true;
    
  } catch (error) {
    console.error('❌ Firebase Storage initialization failed:', error.message);
    console.error('📝 Full error:', error);
    
    if (error.message.includes('storage/bucket-not-found')) {
      console.log('\n💡 Bucket not found - this is expected for new projects');
      console.log('The bucket will be created automatically when you first upload a file');
    } else if (error.message.includes('auth')) {
      console.log('\n💡 Authentication issue - make sure you\'re logged in to Firebase');
    } else {
      console.log('\n💡 Unexpected error - let\'s try alternative approaches');
    }
    
    return false;
  }
}

console.log('🚀 Testing Firebase Storage initialization...\n');
initializeFirebaseStorage();
