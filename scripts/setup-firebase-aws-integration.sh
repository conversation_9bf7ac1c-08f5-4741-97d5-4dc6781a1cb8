#!/bin/bash

# Firebase-AWS Integration Setup Script
# Creates IAM role that can be assumed using Firebase ID tokens

set -e  # Exit on any error

# Configuration
ROLE_NAME="BitByBitFirebaseRole"
POLICY_NAME="BitByBitS3UploadPolicy"
FIREBASE_PROJECT_ID="bitbybit-courses"
ADMIN_EMAIL="<EMAIL>"

echo "🔗 Setting up Firebase-AWS integration..."
echo "🎯 Role name: $ROLE_NAME"
echo "📧 Admin email: $ADMIN_EMAIL"
echo ""

# Get AWS account ID
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
echo "🏢 AWS Account ID: $AWS_ACCOUNT_ID"

# Step 1: Create trust policy for Firebase
echo ""
echo "1️⃣ Creating IAM role trust policy..."

cat > /tmp/trust-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "Federated": "arn:aws:iam::$AWS_ACCOUNT_ID:oidc-provider/securetoken.google.com/$FIREBASE_PROJECT_ID"
            },
            "Action": "sts:AssumeRoleWithWebIdentity",
            "Condition": {
                "StringEquals": {
                    "securetoken.google.com/$FIREBASE_PROJECT_ID:aud": "$FIREBASE_PROJECT_ID",
                    "securetoken.google.com/$FIREBASE_PROJECT_ID:email": "$ADMIN_EMAIL"
                }
            }
        }
    ]
}
EOF

# Step 2: Create OIDC provider for Firebase (if it doesn't exist)
echo ""
echo "2️⃣ Setting up OIDC provider for Firebase..."

OIDC_PROVIDER_ARN="arn:aws:iam::$AWS_ACCOUNT_ID:oidc-provider/securetoken.google.com/$FIREBASE_PROJECT_ID"

if aws iam get-open-id-connect-provider --open-id-connect-provider-arn "$OIDC_PROVIDER_ARN" 2>/dev/null; then
    echo "✅ OIDC provider already exists"
else
    # Get Firebase OIDC configuration
    echo "📡 Fetching Firebase OIDC configuration..."

    # Create OIDC provider
    aws iam create-open-id-connect-provider \
        --url "https://securetoken.google.com/$FIREBASE_PROJECT_ID" \
        --client-id-list "$FIREBASE_PROJECT_ID" \
        --thumbprint-list "1f525b0b1b5b5b5b5b5b5b5b5b5b5b5b5b5b5b5b"

    echo "✅ Created OIDC provider for Firebase"
fi

# Step 3: Create IAM role
echo ""
echo "3️⃣ Creating IAM role..."

if aws iam get-role --role-name "$ROLE_NAME" 2>/dev/null; then
    echo "✅ Role $ROLE_NAME already exists"

    # Update trust policy
    aws iam update-assume-role-policy \
        --role-name "$ROLE_NAME" \
        --policy-document file:///tmp/trust-policy.json
    echo "✅ Updated trust policy"
else
    aws iam create-role \
        --role-name "$ROLE_NAME" \
        --assume-role-policy-document file:///tmp/trust-policy.json \
        --description "Role for Bit by Bit Firebase admin to access S3"
    echo "✅ Created IAM role: $ROLE_NAME"
fi

# Step 4: Attach S3 policy to role
echo ""
echo "4️⃣ Attaching S3 policy to role..."

POLICY_ARN="arn:aws:iam::$AWS_ACCOUNT_ID:policy/$POLICY_NAME"

aws iam attach-role-policy \
    --role-name "$ROLE_NAME" \
    --policy-arn "$POLICY_ARN"

echo "✅ Attached S3 policy to role"

# Step 5: Create configuration file
echo ""
echo "5️⃣ Creating configuration file..."

ROLE_ARN="arn:aws:iam::$AWS_ACCOUNT_ID:role/$ROLE_NAME"

cat > aws-config.json << EOF
{
    "region": "ca-central-1",
    "bucketName": "bitbybit-course-images",
    "roleArn": "$ROLE_ARN",
    "firebaseProjectId": "$FIREBASE_PROJECT_ID",
    "adminEmail": "$ADMIN_EMAIL"
}
EOF

echo "✅ Created aws-config.json"

echo ""
echo "🎉 Firebase-AWS integration setup completed!"
echo ""
echo "📋 Configuration:"
echo "   • IAM Role: $ROLE_ARN"
echo "   • S3 Policy: $POLICY_ARN"
echo "   • OIDC Provider: $OIDC_PROVIDER_ARN"
echo "   • Admin Email: $ADMIN_EMAIL"
echo ""
echo "📄 Configuration saved to: aws-config.json"
echo ""
echo "Next steps:"
echo "1. Add AWS configuration to .env.local"
echo "2. Install AWS SDK: npm install @aws-sdk/client-s3 @aws-sdk/s3-request-presigner @aws-sdk/client-sts"
echo "3. Test the integration"

# Clean up temp files
rm -f /tmp/trust-policy.json
