// Test S3 integration from Node.js environment
const { S3Client, ListObjectsV2Command } = require('@aws-sdk/client-s3');

async function testS3Integration() {
  console.log('🧪 Testing S3 integration...');
  
  const bucketName = 'bitbybit-course-images';
  const region = 'ca-central-1';
  
  try {
    // Test basic S3 connection with your AWS credentials
    const s3Client = new S3Client({ region });
    
    console.log('📡 Testing S3 bucket access...');
    
    const command = new ListObjectsV2Command({
      Bucket: bucketName,
      MaxKeys: 1
    });
    
    const response = await s3Client.send(command);
    
    console.log('✅ S3 bucket is accessible!');
    console.log('📊 Bucket details:');
    console.log(`   • Name: ${bucketName}`);
    console.log(`   • Region: ${region}`);
    console.log(`   • Objects: ${response.KeyCount || 0}`);
    
    if (response.Contents && response.Contents.length > 0) {
      console.log('📁 Sample objects:');
      response.Contents.forEach(obj => {
        console.log(`   • ${obj.Key} (${obj.Size} bytes)`);
      });
    }
    
    console.log('\n🎉 S3 integration test passed!');
    console.log('\n📋 Next steps:');
    console.log('1. Restart your development server');
    console.log('2. Go to Admin Dashboard → Courses');
    console.log('3. Click "Add New Course"');
    console.log('4. Test image upload in the Images section');
    
    return true;
    
  } catch (error) {
    console.error('❌ S3 integration test failed:', error.message);
    
    if (error.name === 'NoSuchBucket') {
      console.log('\n💡 Bucket not found. Please check:');
      console.log('1. Bucket name is correct: bitbybit-course-images');
      console.log('2. Bucket exists in ca-central-1 region');
      console.log('3. Run: aws s3 ls s3://bitbybit-course-images');
    } else if (error.name === 'AccessDenied') {
      console.log('\n💡 Access denied. Please check:');
      console.log('1. AWS credentials are configured');
      console.log('2. IAM permissions for S3 access');
      console.log('3. Run: aws sts get-caller-identity');
    } else {
      console.log('\n💡 Unexpected error. Please check:');
      console.log('1. AWS CLI is configured: aws configure list');
      console.log('2. Internet connection');
      console.log('3. AWS service status');
    }
    
    return false;
  }
}

console.log('🚀 Starting S3 integration test...\n');
testS3Integration();
