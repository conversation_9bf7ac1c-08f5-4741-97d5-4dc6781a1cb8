// Test actual file upload to trigger bucket creation
const { initializeApp } = require('firebase/app');
const { getStorage, ref, uploadBytes, getDownloadURL } = require('firebase/storage');
const fs = require('fs');

// Your Firebase config
const firebaseConfig = {
  apiKey: "AIzaSyA-tPofXKa8nzrLXI12epnAwMS9H2pvaUs",
  authDomain: "bitbybit-courses.firebaseapp.com",
  projectId: "bitbybit-courses",
  storageBucket: "bitbybit-courses.appspot.com",
  messagingSenderId: "377798004351",
  appId: "1:377798004351:web:65d643d612b8aafb29b94a"
};

async function testUpload() {
  try {
    console.log('🔥 Testing Firebase Storage upload...');
    
    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    const storage = getStorage(app);
    
    console.log('✅ Firebase initialized');
    console.log('📁 Storage bucket:', storage.app.options.storageBucket);
    
    // Create a simple test file
    const testContent = 'Firebase Storage test file created at ' + new Date().toISOString();
    const testBuffer = Buffer.from(testContent, 'utf8');
    
    // Create storage reference
    const storageRef = ref(storage, 'test/upload-test.txt');
    console.log('📝 Created storage reference:', storageRef.fullPath);
    
    // Try to upload
    console.log('📤 Attempting upload...');
    const snapshot = await uploadBytes(storageRef, testBuffer, {
      contentType: 'text/plain'
    });
    
    console.log('✅ Upload successful!');
    console.log('📊 Upload details:');
    console.log('   - Bytes transferred:', snapshot.totalBytes);
    console.log('   - Full path:', snapshot.ref.fullPath);
    
    // Get download URL
    const downloadURL = await getDownloadURL(snapshot.ref);
    console.log('🔗 Download URL:', downloadURL);
    
    console.log('\n🎉 Firebase Storage is working perfectly!');
    console.log('💡 The bucket was created automatically');
    console.log('🚀 Now you can deploy storage rules and use image uploads');
    
    return true;
    
  } catch (error) {
    console.error('❌ Upload test failed:', error.message);
    console.error('📝 Error code:', error.code);
    
    if (error.code === 'storage/unauthorized') {
      console.log('\n💡 Unauthorized - this is expected without auth');
      console.log('The bucket exists but requires authentication');
      console.log('This is actually good news - Storage is working!');
      return true;
    } else if (error.code === 'storage/bucket-not-found') {
      console.log('\n💡 Bucket not found - need to enable Storage first');
      return false;
    } else {
      console.log('\n💡 Unexpected error:', error.code);
      return false;
    }
  }
}

console.log('🚀 Running Firebase Storage upload test...\n');
testUpload()
  .then((success) => {
    if (success) {
      console.log('\n✅ Test completed successfully!');
      console.log('Next: Deploy storage rules with: firebase deploy --only storage:rules');
    } else {
      console.log('\n❌ Test failed - Storage needs to be enabled first');
    }
  });
