// Update AdminBuddy V2 data to use your actual user ID as tenant ID
const { initializeApp } = require('firebase/app');
const { getFirestore, collection, getDocs, doc, updateDoc, query, where } = require('firebase/firestore');

// Firebase config
const firebaseConfig = {
  apiKey: "AIzaSyA-tPofXKa8nzrLXI12epnAwMS9H2pvaUs",
  authDomain: "bitbybit-courses.firebaseapp.com",
  projectId: "bitbybit-courses",
  storageBucket: "bitbybit-courses.appspot.com",
  messagingSenderId: "377798004351",
  appId: "1:377798004351:web:65d643d612b8aafb29b94a"
};

// Collection names
const COLLECTIONS = {
  LOCATIONS: "adminbuddy_locations",
  ROUTINES: "adminbuddy_routines", 
  ROUTINE_TASKS: "adminbuddy_routine_tasks",
  LOCATION_ROUTINE_SCHEDULES: "adminbuddy_location_routine_schedules",
  TENANT_ROLES: "adminbuddy_tenant_roles",
};

const OLD_TENANT_ID = "demo-tenant-123";

async function updateTenantData(newTenantId) {
  if (!newTenantId) {
    console.error('❌ Please provide a new tenant ID');
    console.log('Usage: node update-tenant-data.js YOUR_USER_ID');
    process.exit(1);
  }

  try {
    console.log('🔥 Initializing Firebase...');
    const app = initializeApp(firebaseConfig);
    const db = getFirestore(app);
    
    console.log(`🔄 Updating tenant ID from "${OLD_TENANT_ID}" to "${newTenantId}"`);
    
    // Update all collections
    const collections = Object.values(COLLECTIONS);
    
    for (const collectionName of collections) {
      console.log(`📝 Updating ${collectionName}...`);
      
      const q = query(
        collection(db, collectionName),
        where("tenantId", "==", OLD_TENANT_ID)
      );
      
      const snapshot = await getDocs(q);
      
      if (snapshot.empty) {
        console.log(`  ⚠️  No documents found in ${collectionName} with old tenant ID`);
        continue;
      }
      
      const updatePromises = snapshot.docs.map(docSnapshot => {
        return updateDoc(doc(db, collectionName, docSnapshot.id), {
          tenantId: newTenantId
        });
      });
      
      await Promise.all(updatePromises);
      console.log(`  ✅ Updated ${snapshot.docs.length} documents in ${collectionName}`);
    }
    
    console.log('🎉 Tenant ID update complete!');
    console.log(`📊 All data now belongs to tenant: ${newTenantId}`);
    
  } catch (error) {
    console.error('❌ Error updating tenant data:', error);
    process.exit(1);
  }
}

// Get tenant ID from command line argument
const newTenantId = process.argv[2];
updateTenantData(newTenantId);
