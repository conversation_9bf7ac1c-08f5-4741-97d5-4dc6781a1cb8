#!/usr/bin/env node

/**
 * Initialize default email templates in Firebase
 * This script creates the default email templates if they don't exist
 */

const { initializeApp } = require("firebase/app");
const {
  getFirestore,
  collection,
  getDocs,
  addDoc,
  Timestamp,
} = require("firebase/firestore");

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.REACT_APP_FIREBASE_API_KEY,
  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,
  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.REACT_APP_FIREBASE_APP_ID,
};

// Default email templates - simplified for testing
const DEFAULT_EMAIL_TEMPLATES = [
  {
    name: "Registration Confirmation",
    subject: "Welcome to {{courseName}} - Registration Confirmed!",
    body: "Hi {{firstName}},\n\nGreat news! Your registration for {{courseName}} has been confirmed.\n\nBest regards,\n{{adminName}}",
    category: "registration",
    variables: ["{{firstName}}", "{{courseName}}", "{{adminName}}"],
    isActive: true,
  },
];

async function initializeEmailTemplates() {
  try {
    console.log("🚀 Initializing Firebase...");

    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    const db = getFirestore(app);

    console.log("📧 Checking existing email templates...");

    // Check if templates already exist
    const templatesRef = collection(db, "emailTemplates");
    const snapshot = await getDocs(templatesRef);

    if (snapshot.size > 0) {
      console.log(
        `✅ Found ${snapshot.size} existing email templates. Skipping initialization.`
      );
      return;
    }

    console.log("📝 Creating default email templates...");

    // Create default templates
    for (const template of DEFAULT_EMAIL_TEMPLATES) {
      const templateData = {
        ...template,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        createdBy: "system",
      };

      console.log(
        "📝 Template data to be created:",
        JSON.stringify(templateData, null, 2)
      );

      await addDoc(templatesRef, templateData);
      console.log(`✅ Created template: ${template.name}`);
    }

    console.log("🎉 Default email templates initialized successfully!");
    console.log(`📊 Created ${DEFAULT_EMAIL_TEMPLATES.length} email templates`);
  } catch (error) {
    console.error("❌ Error initializing email templates:", error);
    process.exit(1);
  }
}

// Run the initialization
if (require.main === module) {
  initializeEmailTemplates();
}

module.exports = { initializeEmailTemplates };
