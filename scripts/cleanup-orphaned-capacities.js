#!/usr/bin/env node

/**
 * Cleanup Orphaned Course Capacities Script
 * Removes course capacity documents that don't have corresponding course documents
 */

const { initializeApp } = require('firebase/app');
const { getFirestore, collection, getDocs, doc, deleteDoc } = require('firebase/firestore');

// Firebase configuration (you'll need to update this with your actual config)
const firebaseConfig = {
  apiKey: process.env.REACT_APP_FIREBASE_API_KEY,
  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,
  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.REACT_APP_FIREBASE_APP_ID,
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

async function cleanupOrphanedCapacities() {
  console.log('🧹 Cleaning up orphaned course capacities...');
  
  try {
    // Get all course documents
    console.log('📚 Fetching all courses...');
    const coursesRef = collection(db, 'courses');
    const coursesSnapshot = await getDocs(coursesRef);
    const courseIds = new Set(coursesSnapshot.docs.map(doc => doc.id));
    
    console.log(`📊 Found ${courseIds.size} course documents`);
    
    // Get all course capacity documents
    console.log('🏫 Fetching all course capacities...');
    const capacitiesRef = collection(db, 'courseCapacities');
    const capacitiesSnapshot = await getDocs(capacitiesRef);
    
    console.log(`📊 Found ${capacitiesSnapshot.docs.length} capacity documents`);
    
    // Find orphaned capacities
    const orphanedCapacities = [];
    
    for (const capacityDoc of capacitiesSnapshot.docs) {
      const capacityData = capacityDoc.data();
      const courseId = capacityData.courseId || capacityDoc.id;
      
      // Check if this capacity has a corresponding course
      if (!courseIds.has(courseId)) {
        orphanedCapacities.push({
          id: capacityDoc.id,
          courseId: courseId,
          title: capacityData.courseTitle || 'Unknown'
        });
      }
    }
    
    console.log(`🔍 Found ${orphanedCapacities.length} orphaned capacity documents`);
    
    if (orphanedCapacities.length === 0) {
      console.log('✅ No orphaned capacities found. Database is clean!');
      return;
    }
    
    // List orphaned capacities
    console.log('\n📋 Orphaned capacity documents:');
    orphanedCapacities.forEach((capacity, index) => {
      console.log(`${index + 1}. ${capacity.title} (ID: ${capacity.courseId})`);
    });
    
    // Delete orphaned capacities
    console.log('\n🗑️ Deleting orphaned capacity documents...');
    
    for (const capacity of orphanedCapacities) {
      try {
        await deleteDoc(doc(db, 'courseCapacities', capacity.id));
        console.log(`✅ Deleted: ${capacity.title}`);
      } catch (error) {
        console.error(`❌ Failed to delete ${capacity.title}:`, error.message);
      }
    }
    
    console.log(`\n🎉 Cleanup complete! Removed ${orphanedCapacities.length} orphaned capacity documents.`);
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  }
}

// Load environment variables
require('dotenv').config({ path: '.env.local' });

console.log('🧹 Course Capacity Cleanup Tool');
console.log('================================\n');

// Check if Firebase config is available
if (!process.env.REACT_APP_FIREBASE_PROJECT_ID) {
  console.error('❌ Firebase configuration not found in .env.local');
  console.log('Please make sure your .env.local file contains Firebase configuration.');
  process.exit(1);
}

console.log(`🔗 Connected to Firebase project: ${process.env.REACT_APP_FIREBASE_PROJECT_ID}`);
console.log('This will remove course capacity documents that don\'t have corresponding course documents.\n');

cleanupOrphanedCapacities();
