const { initializeApp } = require('firebase/app');
const { getStorage, ref, uploadBytes } = require('firebase/storage');

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDJqGKJqGKJqGKJqGKJqGKJqGKJqGKJqGK",
  authDomain: "bitbybit-courses.firebaseapp.com",
  projectId: "bitbybit-courses",
  storageBucket: "bitbybit-courses.appspot.com",
  messagingSenderId: "123456789012",
  appId: "1:123456789012:web:abcdefghijklmnop"
};

async function enableStorage() {
  try {
    console.log('🔥 Attempting to initialize Firebase Storage...');
    
    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    
    // Get Storage instance (this should trigger Storage creation)
    const storage = getStorage(app);
    
    console.log('✅ Firebase Storage initialized successfully!');
    console.log('📁 Storage bucket:', storage.app.options.storageBucket);
    
    // Try to create a test reference to ensure Storage is working
    const testRef = ref(storage, 'test/init.txt');
    console.log('✅ Storage reference created successfully!');
    
    console.log('\n🎉 Firebase Storage is now enabled!');
    console.log('You can now deploy storage rules with: firebase deploy --only storage:rules');
    
  } catch (error) {
    console.error('❌ Error enabling Firebase Storage:', error.message);
    console.log('\n💡 Alternative solutions:');
    console.log('1. Try the Firebase Console again: https://console.firebase.google.com/project/bitbybit-courses/storage');
    console.log('2. Check if you have the necessary permissions');
    console.log('3. Try using a different browser or incognito mode');
    console.log('4. Clear browser cache and try again');
  }
}

enableStorage();
