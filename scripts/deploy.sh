#!/bin/bash

# Bit By Bit - Production Deployment Script
# This script builds the project and deploys it to AWS S3 with proper configuration

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Load configuration from file if it exists
if [ -f "deploy.config" ]; then
    source deploy.config
    print_status "Loaded configuration from deploy.config"
fi

# Default configuration
BUCKET_NAME="${BUCKET_NAME:-}"
REGION="${REGION:-us-east-1}"
CLOUDFRONT_DISTRIBUTION_ID="${CLOUDFRONT_DISTRIBUTION_ID:-}"
DOMAIN_NAME="${DOMAIN_NAME:-}"
SETUP_HTTPS="${SETUP_HTTPS:-true}"
CERTIFICATE_ARN="${CERTIFICATE_ARN:-}"

# Function to check if AWS CLI is installed and configured
check_aws_cli() {
    print_status "Checking AWS CLI..."

    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed. Please install it first."
        echo "Install: https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html"
        exit 1
    fi

    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS CLI is not configured. Please run 'aws configure' first."
        exit 1
    fi

    print_success "AWS CLI is configured"
}

# Function to validate AWS region
validate_region() {
    print_status "Validating AWS region: $REGION"

    # List of valid AWS regions (as of 2024)
    valid_regions="us-east-1 us-east-2 us-west-1 us-west-2 eu-west-1 eu-west-2 eu-west-3 eu-central-1 eu-north-1 ap-southeast-1 ap-southeast-2 ap-northeast-1 ap-northeast-2 ap-south-1 ca-central-1 sa-east-1"

    if echo "$valid_regions" | grep -q "\b$REGION\b"; then
        print_success "Region $REGION is valid"
    else
        print_warning "Region $REGION might not be valid or supported."
        print_warning "Common regions: us-east-1, us-west-2, eu-west-1, ap-southeast-1"
        print_warning "Continuing anyway..."
    fi
}

# Function to get bucket name from user if not set
get_bucket_name() {
    if [ -z "$BUCKET_NAME" ]; then
        echo -e "${YELLOW}Enter your S3 bucket name:${NC}"
        read -r BUCKET_NAME

        if [ -z "$BUCKET_NAME" ]; then
            print_error "Bucket name is required"
            exit 1
        fi
    fi

    print_status "Using bucket: $BUCKET_NAME"
}

# Function to check if bucket exists
check_bucket_exists() {
    print_status "Checking if bucket exists..."

    if aws s3api head-bucket --bucket "$BUCKET_NAME" --region "$REGION" 2>/dev/null; then
        print_success "Bucket $BUCKET_NAME exists"
    else
        print_warning "Bucket $BUCKET_NAME does not exist. Creating it..."

        if [ "$REGION" = "us-east-1" ]; then
            # us-east-1 doesn't need LocationConstraint
            aws s3api create-bucket --bucket "$BUCKET_NAME" --region "$REGION"
        else
            # All other regions need LocationConstraint
            aws s3api create-bucket --bucket "$BUCKET_NAME" --region "$REGION" \
                --create-bucket-configuration LocationConstraint="$REGION"
        fi

        if [ $? -eq 0 ]; then
            print_success "Bucket created successfully in region $REGION"
        else
            print_error "Failed to create bucket. Please check:"
            print_error "1. Bucket name '$BUCKET_NAME' is globally unique"
            print_error "2. You have permissions to create buckets in region '$REGION'"
            print_error "3. Region '$REGION' is valid"
            exit 1
        fi
    fi
}

# Function to build the project
build_project() {
    print_status "Building project for production..."

    # Clean previous build
    if [ -d "build" ]; then
        rm -rf build >/dev/null 2>&1
    fi

    # Install dependencies if node_modules doesn't exist
    if [ ! -d "node_modules" ]; then
        print_status "Installing dependencies..."
        npm install >/dev/null 2>&1
        if [ $? -ne 0 ]; then
            print_error "Failed to install dependencies"
            exit 1
        fi
    fi

    # Build project with optimized settings (suppress output unless error)
    GENERATE_SOURCEMAP=false \
    BUILD_PATH=build \
    npm run build:production >/dev/null 2>&1

    if [ $? -ne 0 ]; then
        print_error "Build failed. Running with verbose output:"
        GENERATE_SOURCEMAP=false \
        BUILD_PATH=build \
        npm run build:production
        exit 1
    fi

    if [ ! -d "build" ]; then
        print_error "Build failed - build directory not found"
        exit 1
    fi

    print_success "Project built successfully"
}

# Function to configure bucket policy
configure_bucket_policy() {
    print_status "Configuring bucket policy for public read access..."

    # Create bucket policy JSON
    cat > /tmp/bucket-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "PublicReadGetObject",
            "Effect": "Allow",
            "Principal": "*",
            "Action": "s3:GetObject",
            "Resource": "arn:aws:s3:::${BUCKET_NAME}/*"
        }
    ]
}
EOF

    # Apply bucket policy
    aws s3api put-bucket-policy --bucket "$BUCKET_NAME" --region "$REGION" --policy file:///tmp/bucket-policy.json

    # Clean up temp file
    rm /tmp/bucket-policy.json

    print_success "Bucket policy configured"
}

# Function to configure static website hosting
configure_static_hosting() {
    print_status "Configuring static website hosting..."

    # Create website configuration JSON
    cat > /tmp/website-config.json << EOF
{
    "IndexDocument": {
        "Suffix": "index.html"
    },
    "ErrorDocument": {
        "Key": "index.html"
    }
}
EOF

    # Apply website configuration
    aws s3api put-bucket-website --bucket "$BUCKET_NAME" --region "$REGION" --website-configuration file:///tmp/website-config.json

    # Clean up temp file
    rm /tmp/website-config.json

    print_success "Static website hosting configured"
}

# Function to disable block public access (required for static hosting)
configure_public_access() {
    print_status "Configuring public access settings..."

    aws s3api put-public-access-block --bucket "$BUCKET_NAME" --region "$REGION" --public-access-block-configuration \
        "BlockPublicAcls=false,IgnorePublicAcls=false,BlockPublicPolicy=false,RestrictPublicBuckets=false"

    print_success "Public access configured"
}

# Function to check if content actually changed using checksums
check_content_changes() {
    echo ""
    echo "📋 DEPLOYMENT ANALYSIS"
    echo "======================"

    # Create checksums for current build
    local build_checksums="/tmp/build-checksums.txt"
    local s3_checksums="/tmp/s3-checksums.txt"
    local changed_files_list="/tmp/changed-files.txt"

    # Generate checksums for build directory (cross-platform)
    if command -v md5sum >/dev/null 2>&1; then
        # Linux/Windows (md5sum)
        find build/ -type f -exec md5sum {} \; | sed 's|build/||' | sort > "$build_checksums"
    elif command -v md5 >/dev/null 2>&1; then
        # macOS (md5)
        find build/ -type f -exec md5 -r {} \; | sed 's|build/||' | sort > "$build_checksums"
    else
        print_warning "No MD5 utility found - falling back to size-based comparison"
        find build/ -type f -exec ls -l {} \; | awk '{print $5, $9}' | sed 's|build/||' | sort > "$build_checksums"
    fi

    local total_files=$(wc -l < "$build_checksums")
    echo "📁 Total files in build: $total_files"

    # Download existing checksums from S3 (if they exist)
    if aws s3 cp s3://"$BUCKET_NAME"/.checksums.txt "$s3_checksums" 2>/dev/null; then
        echo "🔍 Comparing with previous deployment..."

        # Compare checksums and identify changed files
        if diff "$build_checksums" "$s3_checksums" > "$changed_files_list" 2>/dev/null; then
            echo ""
            echo "✅ RESULT: No changes detected"
            echo "   All $total_files files are identical to previous deployment"
            echo "   Skipping upload to save bandwidth"
            echo ""
            rm -f "$build_checksums" "$s3_checksums" "$changed_files_list"
            return 1  # No changes
        else
            # Parse changed files
            local new_files=$(grep "^< " "$changed_files_list" | wc -l)
            local modified_files=$(grep "^> " "$changed_files_list" | wc -l)
            local total_changes=$((new_files + modified_files))

            echo ""
            echo "📤 UPLOAD REQUIRED"
            echo "=================="
            echo "📊 Changes detected: $total_changes of $total_files files"

            if [ "$total_changes" -le 10 ]; then
                echo "📋 Files to upload:"
                # Show new/modified files
                grep "^< " "$changed_files_list" | sed 's/^< [a-f0-9]* /   ✨ /' | head -10
                grep "^> " "$changed_files_list" | sed 's/^> [a-f0-9]* /   📝 /' | head -10
            else
                echo "📋 Sample of files to upload (showing first 10):"
                (grep "^< " "$changed_files_list" | sed 's/^< [a-f0-9]* /   ✨ /'; grep "^> " "$changed_files_list" | sed 's/^> [a-f0-9]* /   📝 /') | head -10
                echo "   ... and $((total_changes - 10)) more files"
            fi
            echo ""
        fi
    else
        echo "🆕 First deployment detected"
        echo ""
        echo "📤 UPLOAD REQUIRED"
        echo "=================="
        echo "📊 All $total_files files will be uploaded"
        echo ""
    fi

    # Upload new checksums file
    aws s3 cp "$build_checksums" s3://"$BUCKET_NAME"/.checksums.txt --cache-control "private, no-cache" >/dev/null 2>&1

    rm -f "$build_checksums" "$s3_checksums" "$changed_files_list"
    return 0  # Changes detected
}

# Function to deploy files to S3
deploy_to_s3() {
    # Check for actual content changes using checksums
    if ! check_content_changes; then
        echo "🎉 DEPLOYMENT COMPLETE"
        echo "====================="
        echo "✅ No upload needed - all files are up to date"
        echo "💰 Bandwidth saved, free tier preserved"
        echo ""
        return 1  # Return 1 to indicate no upload occurred
    fi

    echo "🚀 UPLOADING FILES"
    echo "=================="

    # Sync static assets (CSS, JS, images) with long cache headers
    echo "📦 Uploading static assets..."
    aws s3 sync build/ s3://"$BUCKET_NAME"/ \
        --delete \
        --cache-control "public, max-age=31536000" \
        --exclude "*.html" \
        --exclude "service-worker.js" \
        --exclude "manifest.json" \
        --exclude ".checksums.txt" \
        >/dev/null 2>&1

    # Upload HTML files and dynamic content with no-cache headers
    echo "📄 Uploading HTML files..."
    aws s3 sync build/ s3://"$BUCKET_NAME"/ \
        --cache-control "no-cache, no-store, must-revalidate" \
        --include "*.html" \
        --include "service-worker.js" \
        --include "manifest.json" \
        >/dev/null 2>&1

    echo "✅ Upload complete"
    echo ""
    return 0  # Return 0 to indicate upload occurred
}

# Function to verify deployment
verify_deployment() {
    print_status "Verifying deployment..."

    # Get website endpoint
    WEBSITE_URL="http://${BUCKET_NAME}.s3-website-${REGION}.amazonaws.com"

    print_status "Testing website accessibility..."

    # Test if website is accessible
    if curl -s --head "$WEBSITE_URL" | head -n 1 | grep -q "200 OK"; then
        print_success "Website is accessible at: $WEBSITE_URL"
    else
        print_warning "Website might not be immediately accessible. DNS propagation may take a few minutes."
        print_status "Website URL: $WEBSITE_URL"
    fi

    # Verify no performance monitoring tools are visible
    print_status "Verifying production build (no dev tools)..."

    if curl -s "$WEBSITE_URL" | grep -q "performance.*dashboard\|📊"; then
        print_error "Performance monitoring tools detected in production build!"
        exit 1
    else
        print_success "Production build verified - no development tools detected"
    fi
}

# Function to request SSL certificate
request_ssl_certificate() {
    if [ -n "$DOMAIN_NAME" ] && [ -z "$CERTIFICATE_ARN" ]; then
        print_status "Requesting SSL certificate for $DOMAIN_NAME..."

        # Request certificate (must be in us-east-1 for CloudFront)
        CERTIFICATE_ARN=$(aws acm request-certificate \
            --domain-name "$DOMAIN_NAME" \
            --domain-name "www.$DOMAIN_NAME" \
            --validation-method DNS \
            --region us-east-1 \
            --query 'CertificateArn' \
            --output text)

        if [ -n "$CERTIFICATE_ARN" ]; then
            print_success "SSL certificate requested: $CERTIFICATE_ARN"
            print_warning "IMPORTANT: You must validate the certificate via DNS before CloudFront setup completes."
            print_warning "Check your email or AWS Console for validation instructions."

            # Update config file with certificate ARN
            if [ -f "deploy.config" ]; then
                if grep -q "CERTIFICATE_ARN" deploy.config; then
                    sed -i.bak "s|CERTIFICATE_ARN=.*|CERTIFICATE_ARN=$CERTIFICATE_ARN|" deploy.config
                else
                    echo "CERTIFICATE_ARN=$CERTIFICATE_ARN" >> deploy.config
                fi
                print_status "Updated deploy.config with certificate ARN"
            fi
        else
            print_error "Failed to request SSL certificate"
            return 1
        fi
    fi
}

# Function to wait for certificate validation
wait_for_certificate() {
    if [ -n "$CERTIFICATE_ARN" ]; then
        print_status "Checking certificate validation status..."

        local max_attempts=60  # 30 minutes max wait
        local attempt=0

        while [ $attempt -lt $max_attempts ]; do
            local status=$(aws acm describe-certificate \
                --certificate-arn "$CERTIFICATE_ARN" \
                --region us-east-1 \
                --query 'Certificate.Status' \
                --output text)

            if [ "$status" = "ISSUED" ]; then
                print_success "SSL certificate validated and issued!"
                return 0
            elif [ "$status" = "FAILED" ]; then
                print_error "SSL certificate validation failed"
                return 1
            else
                print_status "Certificate status: $status (waiting for validation...)"
                sleep 30
                attempt=$((attempt + 1))
            fi
        done

        print_warning "Certificate validation is taking longer than expected."
        print_warning "You can continue with deployment and the certificate will be applied once validated."
    fi
}

# Function to create CloudFront distribution
create_cloudfront_distribution() {
    if [ "$SETUP_HTTPS" = "true" ] && [ -z "$CLOUDFRONT_DISTRIBUTION_ID" ]; then
        print_status "Creating CloudFront distribution for HTTPS..."

        # Create distribution configuration
        local config_file="/tmp/cloudfront-config.json"

        if [ -n "$CERTIFICATE_ARN" ] && [ -n "$DOMAIN_NAME" ]; then
            # With custom domain and SSL
            cat > "$config_file" << EOF
{
    "CallerReference": "bitbybit-$(date +%s)",
    "Comment": "Bit By Bit Website - HTTPS Distribution",
    "DefaultCacheBehavior": {
        "TargetOriginId": "S3-$BUCKET_NAME",
        "ViewerProtocolPolicy": "redirect-to-https",
        "TrustedSigners": {
            "Enabled": false,
            "Quantity": 0
        },
        "ForwardedValues": {
            "QueryString": false,
            "Cookies": {
                "Forward": "none"
            }
        },
        "MinTTL": 0,
        "DefaultTTL": 86400,
        "MaxTTL": 31536000,
        "Compress": true
    },
    "Origins": {
        "Quantity": 1,
        "Items": [
            {
                "Id": "S3-$BUCKET_NAME",
                "DomainName": "$BUCKET_NAME.s3-website-$REGION.amazonaws.com",
                "CustomOriginConfig": {
                    "HTTPPort": 80,
                    "HTTPSPort": 443,
                    "OriginProtocolPolicy": "http-only"
                }
            }
        ]
    },
    "Aliases": {
        "Quantity": 2,
        "Items": ["$DOMAIN_NAME", "www.$DOMAIN_NAME"]
    },
    "ViewerCertificate": {
        "ACMCertificateArn": "$CERTIFICATE_ARN",
        "SSLSupportMethod": "sni-only",
        "MinimumProtocolVersion": "TLSv1.2_2021"
    },
    "CustomErrorResponses": {
        "Quantity": 1,
        "Items": [
            {
                "ErrorCode": 404,
                "ResponsePagePath": "/index.html",
                "ResponseCode": "200",
                "ErrorCachingMinTTL": 300
            }
        ]
    },
    "Enabled": true,
    "PriceClass": "PriceClass_100"
}
EOF
        else
            # Without custom domain (CloudFront default domain with free SSL)
            cat > "$config_file" << EOF
{
    "CallerReference": "bitbybit-$(date +%s)",
    "Comment": "Bit By Bit Website - HTTPS Distribution",
    "DefaultCacheBehavior": {
        "TargetOriginId": "S3-$BUCKET_NAME",
        "ViewerProtocolPolicy": "redirect-to-https",
        "TrustedSigners": {
            "Enabled": false,
            "Quantity": 0
        },
        "ForwardedValues": {
            "QueryString": false,
            "Cookies": {
                "Forward": "none"
            }
        },
        "MinTTL": 0,
        "DefaultTTL": 86400,
        "MaxTTL": 31536000,
        "Compress": true
    },
    "Origins": {
        "Quantity": 1,
        "Items": [
            {
                "Id": "S3-$BUCKET_NAME",
                "DomainName": "$BUCKET_NAME.s3-website-$REGION.amazonaws.com",
                "CustomOriginConfig": {
                    "HTTPPort": 80,
                    "HTTPSPort": 443,
                    "OriginProtocolPolicy": "http-only"
                }
            }
        ]
    },
    "ViewerCertificate": {
        "CloudFrontDefaultCertificate": true
    },
    "CustomErrorResponses": {
        "Quantity": 1,
        "Items": [
            {
                "ErrorCode": 404,
                "ResponsePagePath": "/index.html",
                "ResponseCode": "200",
                "ErrorCachingMinTTL": 300
            }
        ]
    },
    "Enabled": true,
    "PriceClass": "PriceClass_100"
}
EOF
        fi

        # Create the distribution
        local result=$(aws cloudfront create-distribution --distribution-config file://"$config_file")
        CLOUDFRONT_DISTRIBUTION_ID=$(echo "$result" | grep -o '"Id": *"[^"]*"' | head -1 | cut -d'"' -f4)

        if [ -n "$CLOUDFRONT_DISTRIBUTION_ID" ]; then
            print_success "CloudFront distribution created: $CLOUDFRONT_DISTRIBUTION_ID"

            # Update config file
            if [ -f "deploy.config" ]; then
                if grep -q "CLOUDFRONT_DISTRIBUTION_ID" deploy.config; then
                    sed -i.bak "s|CLOUDFRONT_DISTRIBUTION_ID=.*|CLOUDFRONT_DISTRIBUTION_ID=$CLOUDFRONT_DISTRIBUTION_ID|" deploy.config
                else
                    echo "CLOUDFRONT_DISTRIBUTION_ID=$CLOUDFRONT_DISTRIBUTION_ID" >> deploy.config
                fi
                print_status "Updated deploy.config with distribution ID"
            fi

            print_warning "CloudFront distribution is being deployed. This can take 15-20 minutes."
        else
            print_error "Failed to create CloudFront distribution"
            return 1
        fi

        # Clean up temp file
        rm -f "$config_file"
    fi
}

# Function to invalidate CloudFront (if distribution ID provided and files were uploaded)
invalidate_cloudfront() {
    local files_uploaded="$1"

    if [ -n "$CLOUDFRONT_DISTRIBUTION_ID" ] && [ "$files_uploaded" = "true" ]; then
        echo "🔄 CACHE INVALIDATION"
        echo "===================="
        echo "📡 Invalidating CloudFront cache..."

        # Create invalidation and capture the ID
        local invalidation_output=$(aws cloudfront create-invalidation \
            --distribution-id "$CLOUDFRONT_DISTRIBUTION_ID" \
            --paths "/*" 2>/dev/null)

        if [ $? -eq 0 ]; then
            local invalidation_id=$(echo "$invalidation_output" | grep -o '"Id": *"[^"]*"' | head -1 | cut -d'"' -f4)
            echo "✅ Invalidation created: $invalidation_id"
            echo "⏱️  Cache refresh typically takes 1-3 minutes"

            # Optionally wait for invalidation to complete
            if [ "${WAIT_FOR_INVALIDATION:-false}" = "true" ]; then
                wait_for_invalidation "$invalidation_id"
            fi
        else
            echo "⚠️  Failed to create CloudFront invalidation"
            echo "   You may need to manually invalidate the distribution"
        fi
        echo ""
    elif [ -n "$CLOUDFRONT_DISTRIBUTION_ID" ] && [ "$files_uploaded" = "false" ]; then
        echo "⏭️  Skipping cache invalidation (no files uploaded)"
        echo ""
    fi
}

# Function to wait for CloudFront invalidation to complete
wait_for_invalidation() {
    local invalidation_id="$1"
    if [ -n "$invalidation_id" ]; then
        print_status "Waiting for CloudFront invalidation to complete..."

        local max_attempts=20  # 10 minutes max wait
        local attempt=0

        while [ $attempt -lt $max_attempts ]; do
            local status=$(aws cloudfront get-invalidation \
                --distribution-id "$CLOUDFRONT_DISTRIBUTION_ID" \
                --id "$invalidation_id" \
                --query 'Invalidation.Status' \
                --output text 2>/dev/null)

            if [ "$status" = "Completed" ]; then
                print_success "CloudFront invalidation completed!"
                return 0
            else
                print_status "Invalidation status: $status (waiting...)"
                sleep 30
                attempt=$((attempt + 1))
            fi
        done

        print_warning "Invalidation is taking longer than expected, but will complete in the background"
    fi
}

# Function to get CloudFront domain
get_cloudfront_domain() {
    if [ -n "$CLOUDFRONT_DISTRIBUTION_ID" ]; then
        aws cloudfront get-distribution \
            --id "$CLOUDFRONT_DISTRIBUTION_ID" \
            --query 'Distribution.DomainName' \
            --output text 2>/dev/null
    fi
}

# Function to check CloudFront distribution status
check_cloudfront_status() {
    if [ -n "$CLOUDFRONT_DISTRIBUTION_ID" ]; then
        print_status "Checking CloudFront distribution status..."

        local status=$(aws cloudfront get-distribution \
            --id "$CLOUDFRONT_DISTRIBUTION_ID" \
            --query 'Distribution.Status' \
            --output text 2>/dev/null)

        local domain=$(get_cloudfront_domain)

        if [ "$status" = "Deployed" ]; then
            print_success "CloudFront distribution is deployed and ready"
            print_status "Testing CloudFront endpoint..."

            # Test if CloudFront is serving content
            if curl -s --head "https://$domain" | head -n 1 | grep -q "200\|301\|302"; then
                print_success "CloudFront is serving content successfully"
                return 0
            else
                print_warning "CloudFront distribution exists but may not be serving content yet"
                return 1
            fi
        else
            print_warning "CloudFront distribution status: $status"
            print_warning "Distribution is still deploying (this can take 15-20 minutes)"
            return 1
        fi
    fi
    return 1
}

# Function to provide fast deployment option (S3 only)
fast_deploy_option() {
    print_status "🚀 Fast Deployment Option Available!"
    echo ""
    echo "CloudFront deployment takes 15-20 minutes, but your site is already live on S3:"
    echo "📍 S3 Website URL: http://${BUCKET_NAME}.s3-website-${REGION}.amazonaws.com"
    echo ""
    echo "Options:"
    echo "1. Use S3 URL immediately (HTTP only, but instant)"
    echo "2. Wait for CloudFront (HTTPS, global CDN, but slower)"
    echo "3. Continue with both (recommended)"
    echo ""

    if [ "${SKIP_CLOUDFRONT_WAIT:-false}" != "true" ]; then
        echo "💡 Tip: Set SKIP_CLOUDFRONT_WAIT=true in deploy.config to skip CloudFront waiting"
    fi
}

# Function to display final summary
display_summary() {
    echo ""
    echo "🎉 Deployment Complete!"
    echo "===================="
    echo "Bucket: $BUCKET_NAME"
    echo "Region: $REGION"

    # S3 Website URL (HTTP only)
    echo "S3 Website URL (HTTP): http://${BUCKET_NAME}.s3-website-${REGION}.amazonaws.com"

    # CloudFront URLs (HTTPS)
    if [ -n "$CLOUDFRONT_DISTRIBUTION_ID" ]; then
        local cf_domain=$(get_cloudfront_domain)
        if [ -n "$cf_domain" ]; then
            echo "CloudFront URL (HTTPS): https://$cf_domain"

            if [ -n "$DOMAIN_NAME" ]; then
                echo "Custom Domain (HTTPS): https://$DOMAIN_NAME"
                echo "Custom Domain (HTTPS): https://www.$DOMAIN_NAME"
                echo ""
                echo "📋 DNS Configuration Required:"
                echo "  Add CNAME records pointing to: $cf_domain"
                echo "  $DOMAIN_NAME -> $cf_domain"
                echo "  www.$DOMAIN_NAME -> $cf_domain"
            fi
        fi
        echo "CloudFront Distribution ID: $CLOUDFRONT_DISTRIBUTION_ID"
    fi

    if [ -n "$CERTIFICATE_ARN" ]; then
        echo "SSL Certificate: $CERTIFICATE_ARN"
    fi

    echo ""
    echo "✅ Verification Checklist:"
    echo "  - Production build created"
    echo "  - Files deployed to S3"
    echo "  - Bucket policy configured"
    echo "  - Static hosting enabled"
    echo "  - Public access configured"
    echo "  - No dev tools in production"

    if [ "$SETUP_HTTPS" = "true" ]; then
        echo "  - HTTPS enabled via CloudFront"
        if [ -n "$CERTIFICATE_ARN" ]; then
            echo "  - SSL certificate configured"
        fi
    fi

    echo ""
    if [ -n "$CLOUDFRONT_DISTRIBUTION_ID" ]; then
        echo "🔒 Your website is now live with HTTPS!"
        echo "⚠️  Note: CloudFront deployment can take 15-20 minutes to fully propagate."
    else
        echo "🌐 Your website is now live!"
        if [ "$SETUP_HTTPS" = "true" ]; then
            echo "⚠️  HTTPS setup was skipped. Run again with DOMAIN_NAME configured for HTTPS."
        fi
    fi
}

# Main execution
main() {
    echo "🚀 Bit By Bit - Production Deployment with HTTPS"
    echo "================================================"

    # Check prerequisites
    check_aws_cli
    validate_region
    get_bucket_name

    # Build and deploy to S3
    build_project
    check_bucket_exists
    configure_public_access
    configure_bucket_policy
    configure_static_hosting

    # Deploy and track if files were uploaded
    local files_uploaded="false"
    if deploy_to_s3; then
        files_uploaded="true"
        verify_deployment
    fi

    # HTTPS Setup (if enabled)
    if [ "$SETUP_HTTPS" = "true" ]; then
        print_status "Setting up HTTPS with CloudFront..."

        # Check if CloudFront distribution already exists and is ready
        if [ -n "$CLOUDFRONT_DISTRIBUTION_ID" ]; then
            if check_cloudfront_status; then
                print_success "Existing CloudFront distribution is ready!"
            else
                print_warning "Existing CloudFront distribution is still deploying"
                fast_deploy_option
            fi
        else
            # Request SSL certificate if domain is provided
            if [ -n "$DOMAIN_NAME" ]; then
                request_ssl_certificate
                wait_for_certificate
            fi

            # Create CloudFront distribution
            create_cloudfront_distribution

            if [ -n "$CLOUDFRONT_DISTRIBUTION_ID" ]; then
                print_warning "New CloudFront distribution created - deployment in progress"
                fast_deploy_option
            fi
        fi
    else
        print_status "HTTPS setup disabled (SETUP_HTTPS=false)"
    fi

    # Finalize
    invalidate_cloudfront "$files_uploaded"
    display_summary

    echo "🎉 DEPLOYMENT COMPLETE"
    echo "====================="
    if [ "$files_uploaded" = "true" ]; then
        echo "✅ Files uploaded and website updated successfully!"
    else
        echo "✅ Website is up to date - no changes needed!"
    fi
    echo ""
}

# Run main function
main "$@"
