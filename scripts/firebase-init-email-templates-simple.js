#!/usr/bin/env node

/**
 * Simple script to initialize default email templates
 * This uses the existing service function
 */

const { initializeApp } = require('firebase/app');
const { initializeDefaultEmailTemplates } = require('../src/services/emailTemplateService');

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.REACT_APP_FIREBASE_API_KEY,
  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,
  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.REACT_APP_FIREBASE_APP_ID,
};

async function initEmailTemplates() {
  try {
    console.log('🚀 Initializing Firebase...');
    
    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    
    console.log('📧 Initializing default email templates...');
    
    // Use the existing service function
    await initializeDefaultEmailTemplates();
    
    console.log('🎉 Email templates initialization complete!');
    
  } catch (error) {
    console.error('❌ Error initializing email templates:', error);
    process.exit(1);
  }
}

// Run the initialization
if (require.main === module) {
  initEmailTemplates();
}

module.exports = { initEmailTemplates };
