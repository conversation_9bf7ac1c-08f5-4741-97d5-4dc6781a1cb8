// Simple script to help get your Firebase user ID
const { initializeApp } = require('firebase/app');
const { getAuth, signInWithEmailAndPassword } = require('firebase/auth');

// Firebase config
const firebaseConfig = {
  apiKey: "AIzaSyA-tPofXKa8nzrLXI12epnAwMS9H2pvaUs",
  authDomain: "bitbybit-courses.firebaseapp.com",
  projectId: "bitbybit-courses",
  storageBucket: "bitbybit-courses.appspot.com",
  messagingSenderId: "377798004351",
  appId: "1:377798004351:web:65d643d612b8aafb29b94a"
};

async function getUserId() {
  const email = process.argv[2];
  const password = process.argv[3];
  
  if (!email || !password) {
    console.error('❌ Please provide email and password');
    console.log('Usage: node get-user-id.js <EMAIL> your-password');
    process.exit(1);
  }

  try {
    console.log('🔥 Initializing Firebase...');
    const app = initializeApp(firebaseConfig);
    const auth = getAuth(app);
    
    console.log('🔐 Signing in...');
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;
    
    console.log('✅ Sign in successful!');
    console.log('👤 Your User ID:', user.uid);
    console.log('📧 Email:', user.email);
    
    console.log('\n🔄 To update tenant data, run:');
    console.log(`node update-tenant-data.js ${user.uid}`);
    
  } catch (error) {
    console.error('❌ Error getting user ID:', error.message);
    process.exit(1);
  }
}

getUserId();
