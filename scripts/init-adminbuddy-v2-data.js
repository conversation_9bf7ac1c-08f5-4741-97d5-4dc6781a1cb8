// Initialize AdminBuddy V2 tenant-aware sample data in Firebase
const { initializeApp } = require('firebase/app');
const { getFirestore, collection, addDoc } = require('firebase/firestore');

// Firebase config - replace with your actual config
const firebaseConfig = {
  apiKey: "AIzaSyA-tPofXKa8nzrLXI12epnAwMS9H2pvaUs",
  authDomain: "bitbybit-courses.firebaseapp.com",
  projectId: "bitbybit-courses",
  storageBucket: "bitbybit-courses.appspot.com",
  messagingSenderId: "377798004351",
  appId: "1:377798004351:web:65d643d612b8aafb29b94a"
};

// Collection names for V2
const COLLECTIONS = {
  LOCATIONS: "adminbuddy_locations",
  ROUTINES: "adminbuddy_routines", 
  ROUTINE_TASKS: "adminbuddy_routine_tasks",
  LOCATION_ROUTINE_SCHEDULES: "adminbuddy_location_routine_schedules",
  TENANT_ROLES: "adminbuddy_tenant_roles",
};

// Sample tenant ID
const SAMPLE_TENANT_ID = "demo-tenant-123";

async function initializeAdminBuddyV2Data() {
  try {
    console.log('🔥 Initializing Firebase...');
    const app = initializeApp(firebaseConfig);
    const db = getFirestore(app);
    
    console.log('👥 Creating tenant roles...');
    
    // Create tenant roles
    const role1Ref = await addDoc(collection(db, COLLECTIONS.TENANT_ROLES), {
      tenantId: SAMPLE_TENANT_ID,
      name: "Any Employee",
      description: "Standard employee - can complete most tasks",
      permissions: ["complete_tasks", "view_tasks"],
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    const role2Ref = await addDoc(collection(db, COLLECTIONS.TENANT_ROLES), {
      tenantId: SAMPLE_TENANT_ID,
      name: "Keyholder",
      description: "Employee with key access - can open/close",
      permissions: ["complete_tasks", "view_tasks", "key_access"],
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    const role3Ref = await addDoc(collection(db, COLLECTIONS.TENANT_ROLES), {
      tenantId: SAMPLE_TENANT_ID,
      name: "Manager",
      description: "Management level - full task access",
      permissions: ["complete_tasks", "view_tasks", "key_access", "manage_tasks"],
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    console.log('✅ Tenant roles created');
    
    console.log('📍 Creating sample locations...');
    
    // Create locations
    const location1Ref = await addDoc(collection(db, COLLECTIONS.LOCATIONS), {
      tenantId: SAMPLE_TENANT_ID,
      name: "Downtown Coffee Shop",
      address: "123 Main St, Downtown",
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    const location2Ref = await addDoc(collection(db, COLLECTIONS.LOCATIONS), {
      tenantId: SAMPLE_TENANT_ID,
      name: "Westside Cafe",
      address: "456 Oak Ave, Westside", 
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    console.log('✅ Locations created:', location1Ref.id, location2Ref.id);
    
    console.log('🔄 Creating sample routines...');
    
    // Create routines
    const routine1Ref = await addDoc(collection(db, COLLECTIONS.ROUTINES), {
      tenantId: SAMPLE_TENANT_ID,
      name: "Opening",
      description: "Morning startup procedures",
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    const routine2Ref = await addDoc(collection(db, COLLECTIONS.ROUTINES), {
      tenantId: SAMPLE_TENANT_ID,
      name: "Closing",
      description: "End of day procedures", 
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    const routine3Ref = await addDoc(collection(db, COLLECTIONS.ROUTINES), {
      tenantId: SAMPLE_TENANT_ID,
      name: "Daily Maintenance",
      description: "Regular upkeep tasks",
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    console.log('✅ Routines created');
    
    console.log('📋 Creating routine tasks...');
    
    // Create routine tasks
    await addDoc(collection(db, COLLECTIONS.ROUTINE_TASKS), {
      tenantId: SAMPLE_TENANT_ID,
      routineId: routine1Ref.id,
      title: "Unlock front door",
      description: "Turn off alarm, unlock main entrance",
      priority: "high",
      requiredRole: role2Ref.id, // Keyholder
      dueTime: "08:00",
      expiryType: "end_of_day",
      order: 1,
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    await addDoc(collection(db, COLLECTIONS.ROUTINE_TASKS), {
      tenantId: SAMPLE_TENANT_ID,
      routineId: routine1Ref.id,
      title: "Turn on coffee machines",
      description: "Start espresso machine, drip coffee, and grinder",
      priority: "high",
      requiredRole: role1Ref.id, // Any Employee
      dueTime: "08:05",
      expiryType: "end_of_day",
      order: 2,
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    await addDoc(collection(db, COLLECTIONS.ROUTINE_TASKS), {
      tenantId: SAMPLE_TENANT_ID,
      routineId: routine2Ref.id,
      title: "Count register",
      description: "Count cash drawer, record amount",
      priority: "high",
      requiredRole: role2Ref.id, // Keyholder
      dueTime: "20:00",
      expiryType: "end_of_day",
      order: 1,
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    await addDoc(collection(db, COLLECTIONS.ROUTINE_TASKS), {
      tenantId: SAMPLE_TENANT_ID,
      routineId: routine3Ref.id,
      title: "Clean restrooms",
      description: "Check supplies, clean surfaces, restock paper",
      priority: "medium",
      requiredRole: role1Ref.id, // Any Employee
      dueTime: "10:00",
      expiryType: "end_of_day",
      order: 1,
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    console.log('✅ Routine tasks created');
    
    console.log('📅 Creating location routine schedules...');
    
    // Create schedules - Opening routine for both locations, weekdays only
    await addDoc(collection(db, COLLECTIONS.LOCATION_ROUTINE_SCHEDULES), {
      tenantId: SAMPLE_TENANT_ID,
      locationId: location1Ref.id,
      routineId: routine1Ref.id,
      daysOfWeek: [1, 2, 3, 4, 5], // Monday-Friday
      startTime: "08:00",
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    await addDoc(collection(db, COLLECTIONS.LOCATION_ROUTINE_SCHEDULES), {
      tenantId: SAMPLE_TENANT_ID,
      locationId: location2Ref.id,
      routineId: routine1Ref.id,
      daysOfWeek: [1, 2, 3, 4, 5], // Monday-Friday
      startTime: "08:00",
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    // Closing routine for both locations, all days
    await addDoc(collection(db, COLLECTIONS.LOCATION_ROUTINE_SCHEDULES), {
      tenantId: SAMPLE_TENANT_ID,
      locationId: location1Ref.id,
      routineId: routine2Ref.id,
      daysOfWeek: [0, 1, 2, 3, 4, 5, 6], // Every day
      startTime: "20:00",
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    await addDoc(collection(db, COLLECTIONS.LOCATION_ROUTINE_SCHEDULES), {
      tenantId: SAMPLE_TENANT_ID,
      locationId: location2Ref.id,
      routineId: routine2Ref.id,
      daysOfWeek: [0, 1, 2, 3, 4, 5, 6], // Every day
      startTime: "20:00",
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    // Daily maintenance for downtown location only
    await addDoc(collection(db, COLLECTIONS.LOCATION_ROUTINE_SCHEDULES), {
      tenantId: SAMPLE_TENANT_ID,
      locationId: location1Ref.id,
      routineId: routine3Ref.id,
      daysOfWeek: [1, 2, 3, 4, 5], // Monday-Friday
      startTime: "10:00",
      isActive: true,
      createdAt: new Date().toISOString(),
    });
    
    console.log('✅ Location routine schedules created');
    
    console.log('🎉 AdminBuddy V2 sample data initialization complete!');
    console.log('📊 Summary:');
    console.log(`  - Tenant ID: ${SAMPLE_TENANT_ID}`);
    console.log('  - 3 Tenant Roles');
    console.log('  - 2 Locations');
    console.log('  - 3 Routines');
    console.log('  - 4 Routine Tasks');
    console.log('  - 5 Location Routine Schedules');
    
  } catch (error) {
    console.error('❌ Error initializing AdminBuddy V2 data:', error);
    process.exit(1);
  }
}

// Run the initialization
if (require.main === module) {
  initializeAdminBuddyV2Data();
}

module.exports = { initializeAdminBuddyV2Data };
