const https = require('https');

async function testStorageConnection() {
  console.log('🔥 Testing Firebase Storage connection...');
  
  const bucketName = 'bitbybit-courses.appspot.com';
  const testUrl = `https://firebasestorage.googleapis.com/v0/b/${bucketName}/o`;
  
  console.log(`📁 Testing bucket: ${bucketName}`);
  console.log(`🔗 URL: ${testUrl}`);
  
  return new Promise((resolve, reject) => {
    const req = https.get(testUrl, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`📊 Response status: ${res.statusCode}`);
        
        if (res.statusCode === 200) {
          console.log('✅ Storage bucket is accessible!');
          console.log('🎉 Firebase Storage is working!');
          console.log('\nNext steps:');
          console.log('1. Restart your development server');
          console.log('2. Deploy storage rules: firebase deploy --only storage:rules');
          console.log('3. Test image upload in admin dashboard');
          resolve(true);
        } else if (res.statusCode === 403) {
          console.log('⚠️  Bucket exists but access is restricted');
          console.log('💡 You may need to set bucket permissions');
          console.log('📝 Response:', data);
          resolve(false);
        } else if (res.statusCode === 404) {
          console.log('❌ Bucket still not found');
          console.log('💡 Please create the bucket in Google Cloud Console first');
          reject(new Error('Bucket not found'));
        } else {
          console.log('⚠️  Unexpected response:', res.statusCode);
          console.log('📝 Response:', data);
          reject(new Error(`Unexpected status: ${res.statusCode}`));
        }
      });
    });
    
    req.on('error', (error) => {
      console.error('❌ Request error:', error.message);
      reject(error);
    });
    
    req.setTimeout(10000, () => {
      console.error('❌ Request timeout');
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

testStorageConnection()
  .then(() => {
    console.log('\n🚀 Ready to proceed with image upload implementation!');
  })
  .catch((error) => {
    console.log('\n❌ Storage test failed:', error.message);
    console.log('\n🔧 Please complete these steps:');
    console.log('1. Go to: https://console.cloud.google.com/storage/browser?project=bitbybit-courses');
    console.log('2. Enable Cloud Storage API if prompted');
    console.log('3. Create bucket: bitbybit-courses.appspot.com');
    console.log('4. Set bucket to public read access');
    console.log('5. Run this test again: node scripts/test-storage-connection.js');
  });
