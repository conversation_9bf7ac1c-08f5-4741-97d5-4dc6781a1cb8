#!/bin/bash

# AWS S3 Setup Script for Bit by Bit Course Images
# This script creates all necessary AWS resources for secure image uploads

set -e  # Exit on any error

# Configuration
PROJECT_NAME="bitbybit-courses"
BUCKET_NAME="bitbybit-course-images"
REGION="ca-central-1"  # Change if you prefer a different region
FIREBASE_PROJECT_ID="bitbybit-courses"

echo "🚀 Setting up AWS S3 for Bit by Bit course images..."
echo "📁 Bucket name: $BUCKET_NAME"
echo "🌍 Region: $REGION"
echo ""

# Step 1: Create S3 bucket
echo "1️⃣ Creating S3 bucket..."
if aws s3api head-bucket --bucket "$BUCKET_NAME" 2>/dev/null; then
    echo "✅ Bucket $BUCKET_NAME already exists"
else
    if [ "$REGION" = "us-east-1" ]; then
        aws s3api create-bucket --bucket "$BUCKET_NAME"
    else
        aws s3api create-bucket --bucket "$BUCKET_NAME" --region "$REGION" \
            --create-bucket-configuration LocationConstraint="$REGION"
    fi
    echo "✅ Created bucket: $BUCKET_NAME"
fi

# Step 2: Configure bucket for public read access
echo ""
echo "2️⃣ Configuring bucket permissions..."

# Block public ACLs but allow public bucket policies
aws s3api put-public-access-block \
    --bucket "$BUCKET_NAME" \
    --public-access-block-configuration \
    "BlockPublicAcls=true,IgnorePublicAcls=true,BlockPublicPolicy=false,RestrictPublicBuckets=false"

echo "✅ Configured public access block"

# Step 3: Create bucket policy for public read access
echo ""
echo "3️⃣ Creating bucket policy..."

cat > /tmp/bucket-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "PublicReadGetObject",
            "Effect": "Allow",
            "Principal": "*",
            "Action": "s3:GetObject",
            "Resource": "arn:aws:s3:::$BUCKET_NAME/*"
        }
    ]
}
EOF

aws s3api put-bucket-policy --bucket "$BUCKET_NAME" --policy file:///tmp/bucket-policy.json
echo "✅ Applied bucket policy for public read access"

# Step 4: Configure CORS
echo ""
echo "4️⃣ Configuring CORS..."

cat > /tmp/cors-config.json << EOF
{
    "CORSRules": [
        {
            "AllowedHeaders": ["*"],
            "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
            "AllowedOrigins": [
                "http://localhost:3000",
                "https://bitbybit.ca",
                "https://www.bitbybit.ca"
            ],
            "ExposeHeaders": ["ETag"],
            "MaxAgeSeconds": 3000
        }
    ]
}
EOF

aws s3api put-bucket-cors --bucket "$BUCKET_NAME" --cors-configuration file:///tmp/cors-config.json
echo "✅ Configured CORS for web uploads"

# Step 5: Create IAM policy for S3 access
echo ""
echo "5️⃣ Creating IAM policy..."

POLICY_NAME="BitByBitS3UploadPolicy"

cat > /tmp/s3-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:PutObject",
                "s3:PutObjectAcl",
                "s3:GetObject",
                "s3:DeleteObject"
            ],
            "Resource": "arn:aws:s3:::$BUCKET_NAME/*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "s3:ListBucket"
            ],
            "Resource": "arn:aws:s3:::$BUCKET_NAME"
        }
    ]
}
EOF

# Check if policy exists
if aws iam get-policy --policy-arn "arn:aws:iam::$(aws sts get-caller-identity --query Account --output text):policy/$POLICY_NAME" 2>/dev/null; then
    echo "✅ Policy $POLICY_NAME already exists"
else
    POLICY_ARN=$(aws iam create-policy \
        --policy-name "$POLICY_NAME" \
        --policy-document file:///tmp/s3-policy.json \
        --query 'Policy.Arn' --output text)
    echo "✅ Created IAM policy: $POLICY_ARN"
fi

echo ""
echo "🎉 AWS S3 setup completed successfully!"
echo ""
echo "📋 Summary:"
echo "   • S3 Bucket: $BUCKET_NAME"
echo "   • Region: $REGION"
echo "   • Public read access: Enabled"
echo "   • CORS: Configured for web uploads"
echo "   • IAM Policy: $POLICY_NAME"
echo ""
echo "🔗 Bucket URL: https://$BUCKET_NAME.s3.$REGION.amazonaws.com"
echo ""
echo "Next steps:"
echo "1. Run: ./scripts/setup-firebase-aws-integration.sh"
echo "2. Update your .env.local with AWS configuration"
echo "3. Test image uploads in admin dashboard"

# Clean up temp files
rm -f /tmp/bucket-policy.json /tmp/cors-config.json /tmp/s3-policy.json
