# Bit By Bit - Production Deployment Script (PowerShell)
# This script builds the project and deploys it to AWS S3 with proper configuration

param(
    [string]$BucketName = "",
    [string]$Region = "us-east-1",
    [string]$CloudFrontDistributionId = ""
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Load configuration from file if it exists
if (Test-Path "deploy.config") {
    Get-Content "deploy.config" | ForEach-Object {
        if ($_ -match "^([^#][^=]*)=(.*)$") {
            Set-Variable -Name $matches[1] -Value $matches[2]
        }
    }
    Write-Host "[INFO] Loaded configuration from deploy.config" -ForegroundColor Blue
}

# Use parameters or config file values
if (-not $BucketName) { $BucketName = $env:BUCKET_NAME }
if (-not $Region) { $Region = if ($env:REGION) { $env:REGION } else { "us-east-1" } }
if (-not $CloudFrontDistributionId) { $CloudFrontDistributionId = $env:CLOUDFRONT_DISTRIBUTION_ID }

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Test-AwsCli {
    Write-Status "Checking AWS CLI..."
    
    try {
        aws --version | Out-Null
    }
    catch {
        Write-Error "AWS CLI is not installed. Please install it first."
        Write-Host "Install: https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html"
        exit 1
    }
    
    try {
        aws sts get-caller-identity | Out-Null
    }
    catch {
        Write-Error "AWS CLI is not configured. Please run 'aws configure' first."
        exit 1
    }
    
    Write-Success "AWS CLI is configured"
}

function Get-BucketName {
    if (-not $script:BucketName) {
        $script:BucketName = Read-Host "Enter your S3 bucket name"
        
        if (-not $script:BucketName) {
            Write-Error "Bucket name is required"
            exit 1
        }
    }
    
    Write-Status "Using bucket: $script:BucketName"
}

function Test-BucketExists {
    Write-Status "Checking if bucket exists..."
    
    try {
        aws s3api head-bucket --bucket $script:BucketName 2>$null
        Write-Success "Bucket $script:BucketName exists"
    }
    catch {
        Write-Warning "Bucket $script:BucketName does not exist. Creating it..."
        
        if ($script:Region -eq "us-east-1") {
            aws s3api create-bucket --bucket $script:BucketName
        }
        else {
            aws s3api create-bucket --bucket $script:BucketName --region $script:Region --create-bucket-configuration LocationConstraint=$script:Region
        }
        
        Write-Success "Bucket created successfully"
    }
}

function Build-Project {
    Write-Status "Building project for production..."
    
    # Clean previous build
    if (Test-Path "build") {
        Remove-Item -Recurse -Force "build"
        Write-Status "Cleaned previous build"
    }
    
    # Install dependencies if node_modules doesn't exist
    if (-not (Test-Path "node_modules")) {
        Write-Status "Installing dependencies..."
        npm install
    }
    
    # Build project
    npm run build:production
    
    if (-not (Test-Path "build")) {
        Write-Error "Build failed - build directory not found"
        exit 1
    }
    
    Write-Success "Project built successfully"
}

function Set-BucketPolicy {
    Write-Status "Configuring bucket policy for public read access..."
    
    $bucketPolicy = @"
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "PublicReadGetObject",
            "Effect": "Allow",
            "Principal": "*",
            "Action": "s3:GetObject",
            "Resource": "arn:aws:s3:::$script:BucketName/*"
        }
    ]
}
"@
    
    $bucketPolicy | Out-File -FilePath "temp-bucket-policy.json" -Encoding UTF8
    aws s3api put-bucket-policy --bucket $script:BucketName --policy file://temp-bucket-policy.json
    Remove-Item "temp-bucket-policy.json"
    
    Write-Success "Bucket policy configured"
}

function Set-StaticHosting {
    Write-Status "Configuring static website hosting..."
    
    $websiteConfig = @"
{
    "IndexDocument": {
        "Suffix": "index.html"
    },
    "ErrorDocument": {
        "Key": "index.html"
    }
}
"@
    
    $websiteConfig | Out-File -FilePath "temp-website-config.json" -Encoding UTF8
    aws s3api put-bucket-website --bucket $script:BucketName --website-configuration file://temp-website-config.json
    Remove-Item "temp-website-config.json"
    
    Write-Success "Static website hosting configured"
}

function Set-PublicAccess {
    Write-Status "Configuring public access settings..."
    
    aws s3api put-public-access-block --bucket $script:BucketName --public-access-block-configuration "BlockPublicAcls=false,IgnorePublicAcls=false,BlockPublicPolicy=false,RestrictPublicBuckets=false"
    
    Write-Success "Public access configured"
}

function Deploy-ToS3 {
    Write-Status "Deploying files to S3..."
    
    # Sync files with proper cache headers
    aws s3 sync build/ s3://$script:BucketName/ --delete --cache-control "public, max-age=31536000" --exclude "*.html" --exclude "service-worker.js" --exclude "manifest.json"
    
    # Upload HTML files with no-cache headers
    aws s3 sync build/ s3://$script:BucketName/ --delete --cache-control "no-cache, no-store, must-revalidate" --include "*.html" --include "service-worker.js" --include "manifest.json"
    
    Write-Success "Files deployed to S3"
}

function Test-Deployment {
    Write-Status "Verifying deployment..."
    
    $websiteUrl = "http://$script:BucketName.s3-website-$script:Region.amazonaws.com"
    
    Write-Status "Testing website accessibility..."
    
    try {
        $response = Invoke-WebRequest -Uri $websiteUrl -Method Head -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Success "Website is accessible at: $websiteUrl"
        }
    }
    catch {
        Write-Warning "Website might not be immediately accessible. DNS propagation may take a few minutes."
        Write-Status "Website URL: $websiteUrl"
    }
    
    # Verify no performance monitoring tools are visible
    Write-Status "Verifying production build (no dev tools)..."
    
    try {
        $content = Invoke-WebRequest -Uri $websiteUrl -UseBasicParsing
        if ($content.Content -match "performance.*dashboard|📊") {
            Write-Error "Performance monitoring tools detected in production build!"
            exit 1
        }
        else {
            Write-Success "Production build verified - no development tools detected"
        }
    }
    catch {
        Write-Status "Could not verify content, but deployment completed"
    }
}

function Invoke-CloudFrontInvalidation {
    if ($script:CloudFrontDistributionId) {
        Write-Status "Invalidating CloudFront distribution..."
        
        aws cloudfront create-invalidation --distribution-id $script:CloudFrontDistributionId --paths "/*" | Out-Null
        
        Write-Success "CloudFront invalidation created"
    }
}

function Show-Summary {
    Write-Host ""
    Write-Host "🎉 Deployment Complete!" -ForegroundColor Green
    Write-Host "===================="
    Write-Host "Bucket: $script:BucketName"
    Write-Host "Region: $script:Region"
    Write-Host "Website URL: http://$script:BucketName.s3-website-$script:Region.amazonaws.com"
    
    if ($script:CloudFrontDistributionId) {
        Write-Host "CloudFront: Invalidation created"
    }
    
    Write-Host ""
    Write-Host "✅ Verification Checklist:"
    Write-Host "  - Production build created"
    Write-Host "  - Files deployed to S3"
    Write-Host "  - Bucket policy configured"
    Write-Host "  - Static hosting enabled"
    Write-Host "  - Public access configured"
    Write-Host "  - No dev tools in production"
    Write-Host ""
    Write-Host "🌐 Your website is now live!" -ForegroundColor Green
}

# Main execution
Write-Host "🚀 Bit By Bit - Production Deployment" -ForegroundColor Cyan
Write-Host "====================================="

try {
    Test-AwsCli
    Get-BucketName
    Build-Project
    Test-BucketExists
    Set-PublicAccess
    Set-BucketPolicy
    Set-StaticHosting
    Deploy-ToS3
    Test-Deployment
    Invoke-CloudFrontInvalidation
    Show-Summary
    
    Write-Success "Deployment completed successfully! 🎉"
}
catch {
    Write-Error "Deployment failed: $($_.Exception.Message)"
    exit 1
}
