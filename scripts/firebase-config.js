#!/usr/bin/env node

/**
 * Firebase Configuration Helper
 * This script helps you input your Firebase configuration
 */

const readline = require('readline');
const fs = require('fs');
const path = require('path');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('🔥 Firebase Configuration Helper');
console.log('=================================\n');

console.log('Please enter your Firebase configuration values.');
console.log('You can find these in your Firebase Console → Project Settings → General → Your apps\n');

const questions = [
  { key: 'REACT_APP_FIREBASE_API_KEY', prompt: 'API Key: ' },
  { key: 'REACT_APP_FIREBASE_AUTH_DOMAIN', prompt: 'Auth Domain (project-id.firebaseapp.com): ' },
  { key: 'REACT_APP_FIREBASE_PROJECT_ID', prompt: 'Project ID: ' },
  { key: 'REACT_APP_FIREBASE_STORAGE_BUCKET', prompt: 'Storage Bucket (project-id.appspot.com): ' },
  { key: 'REACT_APP_FIREBASE_MESSAGING_SENDER_ID', prompt: 'Messaging Sender ID: ' },
  { key: 'REACT_APP_FIREBASE_APP_ID', prompt: 'App ID: ' }
];

const config = {};

async function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question.prompt, (answer) => {
      config[question.key] = answer.trim();
      resolve();
    });
  });
}

async function collectConfig() {
  console.log('📝 Enter your Firebase configuration:\n');
  
  for (const question of questions) {
    await askQuestion(question);
  }
  
  // Add admin email
  config['REACT_APP_ADMIN_EMAIL'] = '<EMAIL>';
  config['REACT_APP_PERFORMANCE_MODE'] = 'false';
  
  rl.close();
  
  // Create .env.local file
  const envPath = path.join(__dirname, '../.env.local');
  const envContent = Object.entries(config)
    .map(([key, value]) => `${key}=${value}`)
    .join('\n');
  
  fs.writeFileSync(envPath, envContent + '\n');
  
  console.log('\n✅ Configuration saved to .env.local');
  console.log('\n🔧 Next steps:');
  console.log('1. Install Firebase: npm install firebase');
  console.log('2. Run setup: npm run firebase:setup');
  console.log('3. Create admin user: npm run firebase:create-admin');
  console.log('4. Initialize data: npm run firebase:init-data');
  console.log('\n🎉 Your Firebase configuration is ready!');
}

collectConfig().catch(console.error);
