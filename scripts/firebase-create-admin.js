#!/usr/bin/env node

/**
 * Firebase Admin User Creation Script
 * Creates the admin user in Firebase Authentication
 */

const readline = require('readline');

console.log('🔐 Firebase Admin User Setup');
console.log('=============================\n');

console.log('This script will help you create an admin user in Firebase Authentication.');
console.log('You need to do this manually in the Firebase Console:\n');

console.log('📋 Steps to create admin user:');
console.log('1. Go to your Firebase Console: https://console.firebase.google.com/');
console.log('2. Select your project: bitbybit-courses');
console.log('3. Click "Authentication" in the left sidebar');
console.log('4. Click the "Users" tab');
console.log('5. Click "Add user" button');
console.log('6. Enter the following details:');
console.log('   Email: <EMAIL>');
console.log('   Password: [Choose a secure password]');
console.log('7. Click "Add user"\n');

console.log('💡 Recommended password requirements:');
console.log('- At least 12 characters long');
console.log('- Mix of uppercase, lowercase, numbers, and symbols');
console.log('- Example: BitByBit2024!Admin#');
console.log('- Store it securely (password manager recommended)\n');

console.log('🔒 Security Notes:');
console.log('- This email will have full admin access to your course data');
console.log('- Only create this user - no other admin users needed');
console.log('- You can change the password anytime in Firebase Console');
console.log('- Enable 2FA on your Google account for extra security\n');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

rl.question('Press Enter when you have created the admin user in Firebase Console...', () => {
  console.log('\n✅ Admin user setup complete!');
  console.log('\n🔧 Next steps:');
  console.log('1. Run: npm run firebase:init-data');
  console.log('2. Test login at: http://localhost:3000/admin');
  console.log('3. Use the email/password you just created');
  console.log('\n🎉 Your admin authentication is ready!');
  rl.close();
});
