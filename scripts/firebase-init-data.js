#!/usr/bin/env node

/**
 * Firebase Data Initialization Script
 * Sets up initial course capacity data in Firestore
 */

console.log('📊 Firebase Data Initialization');
console.log('===============================\n');

console.log('This script will initialize your Firestore database with course capacity data.');
console.log('You need to do this manually in the Firebase Console:\n');

console.log('📋 Steps to initialize course data:');
console.log('1. Go to your Firebase Console: https://console.firebase.google.com/');
console.log('2. Select your project: bitbybit-courses');
console.log('3. Click "Firestore Database" in the left sidebar');
console.log('4. Click "Start collection"');
console.log('5. Collection ID: courseCapacities');
console.log('6. Add the following documents:\n');

const courseData = [
  { id: 'python-basics', title: 'Python for Beginners', maxStudents: 8 },
  { id: 'web-development', title: 'Web Development Fundamentals', maxStudents: 8 },
  { id: 'javascript-fundamentals', title: 'JavaScript Fundamentals', maxStudents: 8 },
  { id: 'react-basics', title: 'React Basics', maxStudents: 6 },
  { id: 'data-science', title: 'Data Science with Python', maxStudents: 6 },
  { id: 'mobile-apps', title: 'Mobile App Development', maxStudents: 6 },
  { id: 'game-development', title: 'Game Development', maxStudents: 8 },
  { id: 'ai-machine-learning', title: 'AI & Machine Learning', maxStudents: 6 }
];

courseData.forEach((course, index) => {
  console.log(`Document ${index + 1}:`);
  console.log(`  Document ID: ${course.id}`);
  console.log(`  Fields:`);
  console.log(`    courseId (string): ${course.id}`);
  console.log(`    courseTitle (string): ${course.title}`);
  console.log(`    maxStudents (number): ${course.maxStudents}`);
  console.log(`    currentEnrollment (number): 0`);
  console.log(`    status (string): open`);
  console.log(`    lastUpdated (timestamp): [Click "Add timestamp"]`);
  console.log('');
});

console.log('💡 Tips:');
console.log('- Make sure field types match exactly (string, number, timestamp)');
console.log('- Document IDs must match the course slugs in your app');
console.log('- You can adjust maxStudents values later through the admin dashboard');
console.log('- The timestamp field helps track when capacities were last updated\n');

console.log('🔄 Alternative: Automatic Setup');
console.log('If you prefer, you can also:');
console.log('1. Build and deploy your app first');
console.log('2. Go to /admin and login');
console.log('3. The app will automatically create missing course capacity documents');
console.log('4. You can then adjust the capacities through the admin interface\n');

const readline = require('readline');
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

rl.question('Press Enter when you have set up the course capacity data...', () => {
  console.log('\n✅ Course capacity data initialized!');
  console.log('\n🎉 Firebase setup is now complete!');
  console.log('\n🚀 Your app is ready for production with:');
  console.log('✅ Real-time course registration tracking');
  console.log('✅ Secure admin authentication');
  console.log('✅ Persistent data storage');
  console.log('✅ Automatic capacity management');
  console.log('✅ Cross-device synchronization');
  console.log('\n🔗 Test your setup:');
  console.log('1. Start your app: npm start');
  console.log('2. Register for a course');
  console.log('3. Check admin dashboard: http://localhost:3000/admin');
  console.log('4. Verify data appears in Firebase Console');
  rl.close();
});
