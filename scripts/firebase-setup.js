#!/usr/bin/env node

/**
 * Firebase Setup Script for Bit by Bit
 * This script helps set up Firebase configuration and initial data
 */

const fs = require('fs');
const path = require('path');

console.log('🔥 Firebase Setup for Bit by Bit');
console.log('================================\n');

// Check if Firebase config exists
const configPath = path.join(__dirname, '../src/config/firebase.ts');
const envPath = path.join(__dirname, '../.env.local');

if (!fs.existsSync(configPath)) {
  console.log('❌ Firebase configuration not found!');
  console.log('\n📋 Please follow these steps:');
  console.log('1. Go to https://console.firebase.google.com/');
  console.log('2. Create a new project: "bitbybit-courses"');
  console.log('3. Enable Authentication (Email/Password)');
  console.log('4. Enable Firestore Database (test mode)');
  console.log('5. Get your web app config');
  console.log('6. Run: npm run firebase:config');
  console.log('\n🔧 Then run this script again: npm run firebase:setup\n');
  process.exit(1);
}

console.log('✅ Firebase configuration found!');
console.log('✅ Setting up Firebase services...\n');

// Create Firestore security rules
const firestoreRules = `rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Public read access to course capacities
    match /courseCapacities/{courseId} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.token.email == '<EMAIL>';
    }
    
    // Admin-only access to registrations
    match /registrations/{registrationId} {
      allow read, write: if request.auth != null && request.auth.token.email == '<EMAIL>';
    }
    
    // Admin-only access to admin settings
    match /adminSettings/{document} {
      allow read, write: if request.auth != null && request.auth.token.email == '<EMAIL>';
    }
  }
}`;

const rulesPath = path.join(__dirname, '../firestore.rules');
fs.writeFileSync(rulesPath, firestoreRules);
console.log('✅ Created Firestore security rules');

// Create Firebase deployment configuration
const firebaseJson = {
  "firestore": {
    "rules": "firestore.rules",
    "indexes": "firestore.indexes.json"
  },
  "hosting": {
    "public": "build",
    "ignore": [
      "firebase.json",
      "**/.*",
      "**/node_modules/**"
    ],
    "rewrites": [
      {
        "source": "**",
        "destination": "/index.html"
      }
    ]
  }
};

fs.writeFileSync(path.join(__dirname, '../firebase.json'), JSON.stringify(firebaseJson, null, 2));
console.log('✅ Created Firebase deployment configuration');

// Create Firestore indexes
const firestoreIndexes = {
  "indexes": [
    {
      "collectionGroup": "registrations",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "courseId",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "timestamp",
          "order": "DESCENDING"
        }
      ]
    },
    {
      "collectionGroup": "registrations",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "timestamp",
          "order": "DESCENDING"
        }
      ]
    }
  ],
  "fieldOverrides": []
};

fs.writeFileSync(path.join(__dirname, '../firestore.indexes.json'), JSON.stringify(firestoreIndexes, null, 2));
console.log('✅ Created Firestore indexes configuration');

// Create environment variables template
const envTemplate = `# Firebase Configuration
# Copy your Firebase config values here

REACT_APP_FIREBASE_API_KEY=your_api_key_here
REACT_APP_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=your_project_id
REACT_APP_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
REACT_APP_FIREBASE_APP_ID=your_app_id

# Admin Configuration
REACT_APP_ADMIN_EMAIL=<EMAIL>

# Performance Monitoring (optional)
REACT_APP_PERFORMANCE_MODE=false
`;

if (!fs.existsSync(envPath)) {
  fs.writeFileSync(envPath, envTemplate);
  console.log('✅ Created .env.local template');
} else {
  console.log('✅ .env.local already exists');
}

console.log('\n🎉 Firebase setup complete!');
console.log('\n📋 Next steps:');
console.log('1. Install Firebase: npm install firebase');
console.log('2. Update your Firebase config in src/config/firebase.ts');
console.log('3. Update .env.local with your Firebase values');
console.log('4. Create admin user: npm run firebase:create-admin');
console.log('5. Initialize data: npm run firebase:init-data');
console.log('\n🚀 Then your app will be ready for production!');
