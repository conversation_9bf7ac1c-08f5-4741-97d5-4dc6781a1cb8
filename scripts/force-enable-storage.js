const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
const serviceAccount = {
  "type": "service_account",
  "project_id": "bitbybit-courses",
  "private_key_id": process.env.FIREBASE_PRIVATE_KEY_ID,
  "private_key": process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  "client_email": process.env.FIREBASE_CLIENT_EMAIL,
  "client_id": process.env.FIREBASE_CLIENT_ID,
  "auth_uri": "https://accounts.google.com/o/oauth2/auth",
  "token_uri": "https://oauth2.googleapis.com/token",
  "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
  "client_x509_cert_url": `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.FIREBASE_CLIENT_EMAIL}`
};

async function forceEnableStorage() {
  try {
    console.log('🔥 Attempting to force enable Firebase Storage...');
    
    // Initialize Admin SDK
    if (!admin.apps.length) {
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        storageBucket: 'bitbybit-courses.appspot.com'
      });
    }
    
    // Get Storage instance
    const bucket = admin.storage().bucket();
    
    console.log('📁 Storage bucket:', bucket.name);
    
    // Try to create a test file to trigger Storage creation
    const testFile = bucket.file('test/storage-init.txt');
    await testFile.save('Firebase Storage initialized successfully!', {
      metadata: {
        contentType: 'text/plain'
      }
    });
    
    console.log('✅ Test file created successfully!');
    
    // Clean up test file
    await testFile.delete();
    console.log('🧹 Test file cleaned up');
    
    console.log('\n🎉 Firebase Storage is now enabled and working!');
    console.log('You can now deploy storage rules with: firebase deploy --only storage:rules');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    
    if (error.message.includes('service account')) {
      console.log('\n💡 This script requires Firebase Admin credentials.');
      console.log('Let\'s try a different approach...');
    } else if (error.message.includes('bucket')) {
      console.log('\n💡 Storage bucket might not exist yet.');
      console.log('This is the core issue we need to solve.');
    }
  }
}

// Alternative: Try with client SDK
async function tryClientSDK() {
  try {
    console.log('\n🔄 Trying with Firebase Client SDK...');
    
    const { initializeApp } = require('firebase/app');
    const { getStorage, ref, uploadBytes } = require('firebase/storage');
    
    const firebaseConfig = {
      apiKey: "AIzaSyBJqGKJqGKJqGKJqGKJqGKJqGKJqGKJqGK", // This will be replaced with real config
      authDomain: "bitbybit-courses.firebaseapp.com",
      projectId: "bitbybit-courses",
      storageBucket: "bitbybit-courses.appspot.com",
      messagingSenderId: "************",
      appId: "1:************:web:abcdefghijklmnop"
    };
    
    const app = initializeApp(firebaseConfig);
    const storage = getStorage(app);
    
    console.log('✅ Client SDK initialized');
    console.log('📁 Storage bucket:', storage.app.options.storageBucket);
    
    // Try to create a reference (this might trigger bucket creation)
    const testRef = ref(storage, 'test/init.txt');
    console.log('✅ Storage reference created');
    
  } catch (error) {
    console.error('❌ Client SDK error:', error.message);
  }
}

console.log('🚀 Starting Firebase Storage enablement process...\n');

forceEnableStorage()
  .then(() => tryClientSDK())
  .catch(error => {
    console.error('❌ Process failed:', error.message);
    console.log('\n🔧 Manual steps required:');
    console.log('1. Go to Google Cloud Console: https://console.cloud.google.com/storage/browser?project=bitbybit-courses');
    console.log('2. Enable Cloud Storage API if prompted');
    console.log('3. Create bucket: bitbybit-courses.appspot.com');
    console.log('4. Then try Firebase Console again');
  });
