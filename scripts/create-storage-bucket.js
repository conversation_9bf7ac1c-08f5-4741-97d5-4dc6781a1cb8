const https = require('https');

async function createStorageBucket() {
  console.log('🔥 Attempting to create Firebase Storage bucket...');
  
  // First, let's try to access the bucket directly to see if it exists
  const bucketName = 'bitbybit-courses.firebasestorage.app';
  
  console.log(`📁 Checking bucket: ${bucketName}`);
  
  // Try to make a simple request to see if the bucket exists
  const testUrl = `https://firebasestorage.googleapis.com/v0/b/${bucketName}/o`;
  
  return new Promise((resolve, reject) => {
    const req = https.get(testUrl, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`📊 Response status: ${res.statusCode}`);
        
        if (res.statusCode === 200) {
          console.log('✅ Storage bucket exists and is accessible!');
          console.log('🎉 Firebase Storage is working!');
          resolve(true);
        } else if (res.statusCode === 404) {
          console.log('❌ Storage bucket does not exist');
          console.log('📝 Response:', data);
          reject(new Error('Bucket not found'));
        } else {
          console.log('⚠️  Unexpected response:', res.statusCode);
          console.log('📝 Response:', data);
          reject(new Error(`Unexpected status: ${res.statusCode}`));
        }
      });
    });
    
    req.on('error', (error) => {
      console.error('❌ Request error:', error.message);
      reject(error);
    });
    
    req.setTimeout(10000, () => {
      console.error('❌ Request timeout');
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

async function tryAlternativeBucket() {
  console.log('\n🔄 Trying alternative bucket name...');
  
  const altBucketName = 'bitbybit-courses.appspot.com';
  const testUrl = `https://firebasestorage.googleapis.com/v0/b/${altBucketName}/o`;
  
  return new Promise((resolve, reject) => {
    const req = https.get(testUrl, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`📊 Alternative bucket response: ${res.statusCode}`);
        
        if (res.statusCode === 200) {
          console.log('✅ Alternative bucket exists!');
          console.log('💡 You should update your .env.local file:');
          console.log('   REACT_APP_FIREBASE_STORAGE_BUCKET=bitbybit-courses.appspot.com');
          resolve(true);
        } else {
          console.log('❌ Alternative bucket also not found');
          reject(new Error('Alternative bucket not found'));
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
  });
}

async function main() {
  try {
    await createStorageBucket();
  } catch (error) {
    console.log('\n🔄 Primary bucket failed, trying alternative...');
    
    try {
      await tryAlternativeBucket();
    } catch (altError) {
      console.log('\n❌ Both bucket attempts failed');
      console.log('\n🔧 Manual steps required:');
      console.log('1. Go to: https://console.cloud.google.com/storage/browser?project=bitbybit-courses');
      console.log('2. Click "CREATE BUCKET"');
      console.log('3. Name: bitbybit-courses.appspot.com');
      console.log('4. Location: us-central1 (or your preferred region)');
      console.log('5. Storage class: Standard');
      console.log('6. Access control: Uniform');
      console.log('7. Click "CREATE"');
      console.log('\nAlternatively:');
      console.log('1. Go to: https://console.firebase.google.com/project/bitbybit-courses/storage');
      console.log('2. Try different browser or incognito mode');
      console.log('3. Clear browser cache and try again');
    }
  }
}

main();
