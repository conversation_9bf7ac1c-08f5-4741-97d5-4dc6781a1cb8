// Update sample data to use proper tenant ID based on your user ID
const { initializeApp } = require('firebase/app');
const { getFirestore, collection, getDocs, doc, updateDoc, query, where } = require('firebase/firestore');

// Firebase config
const firebaseConfig = {
  apiKey: "AIzaSyA-tPofXKa8nzrLXI12epnAwMS9H2pvaUs",
  authDomain: "bitbybit-courses.firebaseapp.com",
  projectId: "bitbybit-courses",
  storageBucket: "bitbybit-courses.appspot.com",
  messagingSenderId: "************",
  appId: "1:************:web:65d643d612b8aafb29b94a"
};

// Collection names
const COLLECTIONS = {
  LOCATIONS: "adminbuddy_locations",
  ROUTINES: "adminbuddy_routines", 
  ROUTINE_TASKS: "adminbuddy_routine_tasks",
  LOCATION_ROUTINE_SCHEDULES: "adminbuddy_location_routine_schedules",
  TENANT_ROLES: "adminbuddy_tenant_roles",
};

const OLD_TENANT_ID = "demo-tenant-123";

async function updateToProperTenant() {
  const userUid = "d3sctaeGjiSxp8URA7XRAl58Mpy2"; // Your user ID
  const newTenantId = `tenant-${userUid.slice(0, 8)}`; // tenant-d3sctaeG
  
  console.log(`🔄 Updating from "${OLD_TENANT_ID}" to proper tenant: "${newTenantId}"`);

  try {
    console.log('🔥 Initializing Firebase...');
    const app = initializeApp(firebaseConfig);
    const db = getFirestore(app);
    
    // Update all collections
    const collections = Object.values(COLLECTIONS);
    let totalUpdated = 0;
    
    for (const collectionName of collections) {
      console.log(`📝 Updating ${collectionName}...`);
      
      const q = query(
        collection(db, collectionName),
        where("tenantId", "==", OLD_TENANT_ID)
      );
      
      const snapshot = await getDocs(q);
      
      if (snapshot.empty) {
        console.log(`  ⚠️  No documents found in ${collectionName} with old tenant ID`);
        continue;
      }
      
      const updatePromises = snapshot.docs.map(docSnapshot => {
        return updateDoc(doc(db, collectionName, docSnapshot.id), {
          tenantId: newTenantId
        });
      });
      
      await Promise.all(updatePromises);
      console.log(`  ✅ Updated ${snapshot.docs.length} documents in ${collectionName}`);
      totalUpdated += snapshot.docs.length;
    }
    
    console.log('🎉 Tenant ID update complete!');
    console.log(`📊 Updated ${totalUpdated} total documents`);
    console.log(`🏢 All data now belongs to tenant: ${newTenantId}`);
    console.log(`👤 This matches your user profile tenant ID pattern`);
    
  } catch (error) {
    console.error('❌ Error updating tenant data:', error);
    process.exit(1);
  }
}

updateToProperTenant();
