import React, { Suspense } from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Layout from "./components/layout/Layout";
import ErrorBoundary from "./components/ui/ErrorBoundary";
import LoadingSpinner from "./components/ui/LoadingSpinner";
import PerformanceDashboard from "./components/dev/PerformanceDashboard";
import { TenantProvider } from "./contexts/TenantContext";
import { ROUTES } from "./constants";
import "./styles/globals.css";

// AdminBuddy Pages - Lazy loaded for better performance
const Home = React.lazy(() => import("./pages/Home"));
const About = React.lazy(() => import("./pages/About"));
const Features = React.lazy(() => import("./pages/Features"));
const Pricing = React.lazy(() => import("./pages/Pricing"));
const Contact = React.lazy(() => import("./pages/Contact"));
const Login = React.lazy(() => import("./pages/Login"));
const SimpleLogin = React.lazy(() => import("./pages/SimpleLogin"));
const Signup = React.lazy(() => import("./pages/Signup"));
const Onboarding = React.lazy(() => import("./pages/Onboarding"));

// Dashboard Pages (no layout)
const OwnerDashboard = React.lazy(() => import("./pages/OwnerDashboard"));
const ManagerDashboard = React.lazy(() => import("./pages/ManagerDashboard"));
const KioskInterface = React.lazy(() => import("./pages/KioskInterface"));
const BillingPage = React.lazy(() => import("./pages/BillingPage/BillingPage"));

// Legacy pages (for reference during transition)
const Admin = React.lazy(() => import("./pages/Admin"));
const Instructor = React.lazy(() => import("./pages/Instructor"));

const LoadingFallback: React.FC = () => (
  <div
    style={{
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      minHeight: "400px",
    }}
  >
    <LoadingSpinner size="large" />
  </div>
);

const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <TenantProvider>
        <Router>
          <Suspense fallback={<LoadingFallback />}>
            <Routes>
              {/* Authentication routes */}
              <Route path="/login" element={<SimpleLogin />} />
              <Route path="/login-firebase" element={<Login />} />
              <Route path="/signup" element={<Signup />} />
              <Route path="/onboarding" element={<Onboarding />} />

              {/* Dashboard routes - full page without layout */}
              <Route path="/dashboard" element={<OwnerDashboard />} />
              <Route
                path="/dashboard/daily-tasks"
                element={<OwnerDashboard />}
              />
              <Route path="/dashboard/reports" element={<OwnerDashboard />} />
              <Route path="/dashboard/locations" element={<OwnerDashboard />} />
              <Route path="/dashboard/routines" element={<OwnerDashboard />} />
              <Route
                path="/dashboard/routine-tasks"
                element={<OwnerDashboard />}
              />
              <Route path="/dashboard/schedules" element={<OwnerDashboard />} />
              <Route path="/dashboard/roles" element={<OwnerDashboard />} />
              <Route path="/dashboard/help" element={<OwnerDashboard />} />
              <Route path="/billing" element={<BillingPage />} />

              {/* Legacy dashboard routes - redirect to new URLs */}
              <Route path="/owner" element={<OwnerDashboard />} />
              <Route path="/owner/:tab" element={<OwnerDashboard />} />
              <Route path="/manager" element={<ManagerDashboard />} />
              <Route path="/manager/:tab" element={<ManagerDashboard />} />
              <Route path="/kiosk" element={<KioskInterface />} />
              <Route path="/kiosk/:locationId" element={<KioskInterface />} />

              {/* Legacy routes (for transition) */}
              <Route path="/admin" element={<Admin />} />
              <Route path="/admin/:tab" element={<Admin />} />
              <Route path="/instructor" element={<Instructor />} />
              <Route path="/instructor/:tab" element={<Instructor />} />

              {/* Marketing site routes with layout */}
              <Route
                path="*"
                element={
                  <Layout>
                    <Routes>
                      <Route path="/" element={<Home />} />
                      <Route path="/features" element={<Features />} />
                      <Route path="/pricing" element={<Pricing />} />
                      <Route path="/about" element={<About />} />
                      <Route path="/contact" element={<Contact />} />
                    </Routes>
                  </Layout>
                }
              />
            </Routes>
          </Suspense>
          {process.env.REACT_APP_PERFORMANCE_MODE === "true" && (
            <PerformanceDashboard />
          )}
        </Router>
      </TenantProvider>
    </ErrorBoundary>
  );
};

export default App;
