// Task completion record for a specific task
export interface TaskCompletionRecord {
  taskId: string;
  completedBy: string; // User ID or name who completed the task
  completedAt: string; // ISO timestamp when task was completed
  status: "completed" | "skipped" | "flagged";
  notes?: string; // Optional completion notes
  timeSpent?: number; // Time in minutes (optional)
}

// Task related types
export interface Task {
  id: string;
  taskGroupId: string; // Task group this task belongs to
  title: string;
  description?: string;
  estimatedTime?: number; // Estimated time in minutes
  priority: "low" | "medium" | "high";
  role?: string; // Role filter (e.g., "Cashier", "Manager", "Cleaner")
  timeOfDay?: "opening" | "closing" | "anytime"; // Time-based filter
  isRequired: boolean; // Can this task be skipped?
  order: number; // Display order within task group
  status: "active" | "inactive";
  createdAt: string;
  updatedAt: string;
  // Completion tracking
  completions?: TaskCompletionRecord[];
  // Optional fields added by services
  locationName?: string;
  taskGroupName?: string;
}

// Task group types (recurring routines like opening/closing checklists)
export type TaskGroupFrequency = "daily" | "weekly" | "monthly" | "custom";
export type TaskGroupType =
  | "opening"
  | "closing"
  | "maintenance"
  | "cleaning"
  | "inventory"
  | "custom";

// Task Group (replaces Course)
export interface TaskGroup {
  id: string;
  tenantId: string; // Which business this belongs to
  locationId: string; // Which location this applies to
  name: string;
  description: string;
  type: TaskGroupType;
  frequency: TaskGroupFrequency;
  schedule?: {
    daysOfWeek: string[]; // e.g., ["Monday", "Wednesday"]
    time?: string; // e.g., "09:00" for opening tasks
  };
  estimatedDuration: number; // Total estimated time in minutes
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: string; // User ID who created this
  // Task management
  tasks?: Task[];
  order: number; // Display order
}

// Location (replaces Course in many contexts)
export interface Location {
  id: string;
  tenantId: string; // Which business this belongs to
  name: string;
  address?: string;
  phone?: string;
  email?: string;
  timezone: string; // e.g., "America/Los_Angeles"
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  // Management
  managerIds: string[]; // Managers assigned to this location
  // Task groups for this location
  taskGroups?: TaskGroup[];
}

// Tenant (Business) - top level organization
export interface Tenant {
  id: string;
  name: string; // Business name
  industry?: string; // e.g., "Restaurant", "Retail", "Cleaning"
  plan: "free" | "basic" | "pro"; // Subscription plan
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  // Trial Management
  trialStartDate?: string; // ISO date when trial started
  trialEndDate?: string; // ISO date when trial ends (14 days from start)
  trialExpired?: boolean; // Computed field for quick checks
  // Billing & Subscription
  stripeCustomerId?: string;
  subscriptionId?: string;
  subscriptionStatus?:
    | "active"
    | "canceled"
    | "past_due"
    | "unpaid"
    | "incomplete";
  subscriptionCurrentPeriodEnd?: string; // ISO date
  subscriptionLocationCount?: number; // Number of locations purchased
  lastPaymentDate?: string; // ISO date of last successful payment
  // Settings
  settings: {
    timezone: string;
    dateFormat: string;
    timeFormat: "12h" | "24h";
  };
}

// User types and roles
export type UserRole = "owner" | "manager" | "kiosk";

export interface User {
  id: string;
  tenantId: string; // Which business they belong to
  email: string;
  name: string;
  role: UserRole;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
  // Role-specific data
  locationIds?: string[]; // For managers - which locations they can access
  kioskLocationId?: string; // For kiosk users - which location they're tied to
}

// Shift handoff notes
export interface ShiftHandoff {
  id: string;
  tenantId: string;
  locationId: string;
  date: string; // YYYY-MM-DD
  shift: "opening" | "closing" | "mid";
  notes: string;
  createdBy: string; // User ID
  createdAt: string;
  // Optional task completion summary
  tasksCompleted?: number;
  tasksSkipped?: number;
  tasksFlagged?: number;
}

// Location Report - flexible reporting system
export interface LocationReport {
  id: string;
  tenantId: string;
  locationId: string;
  locationName: string;
  reportTimestamp: string; // When report was created
  reportedBy: string; // user email/name

  // Report Period (since last report)
  reportPeriod: {
    fromTimestamp: string; // Last report time (or beginning of time)
    toTimestamp: string; // This report time
  };

  // Task Summary for this period
  taskSummary: {
    totalTasksInPeriod: number;
    completedInPeriod: number;
    currentPending: number;
    currentExpired: number;
  };

  // Tasks completed since last report
  completedSinceLastReport: Array<{
    id: string;
    title: string;
    routineName?: string;
    requiredRole?: string;
    completedAt: string;
    notes?: string;
  }>;

  // Current state of pending/expired tasks
  currentPendingTasks: Array<{
    id: string;
    title: string;
    routineName?: string;
    requiredRole?: string;
    dueTime?: string;
    notes?: string;
    status: "pending" | "expired";
  }>;

  // User notes for this report
  reportNotes: string;

  createdAt: string;
}

// UI Component types
export interface ButtonProps {
  children: React.ReactNode;
  variant?: "primary" | "secondary";
  size?: "small" | "medium" | "large";
  href?: string;
  target?: string;
  rel?: string;
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
  className?: string;
  type?: "button" | "submit" | "reset";
}

export interface TaskGroupCardProps {
  taskGroup: TaskGroup;
  onEdit?: (taskGroup: TaskGroup) => void;
  onDelete?: (taskGroupId: string) => void;
}

export interface LocationCardProps {
  location: Location;
  onEdit?: (location: Location) => void;
  onDelete?: (locationId: string) => void;
}

// Layout types
export interface LayoutProps {
  children: React.ReactNode;
}

// Navigation types
export interface NavigationItem {
  to: string;
  label: string;
}

// Hook types
export interface UseLocationsReturn {
  locations: Location[];
  loading: boolean;
  error: string | null;
  getLocationById: (id: string) => Location | undefined;
}

export interface UseTaskGroupsReturn {
  taskGroups: TaskGroup[];
  loading: boolean;
  error: string | null;
  getTaskGroupById: (id: string) => TaskGroup | undefined;
}

// Legacy types (for transition period)
export interface CourseSession {
  id: string;
  courseId: string;
  sessionNumber: number;
  date: string;
  startTime: string;
  endTime: string;
  topic?: string;
  description?: string;
  location?: string;
  instructorNotes?: string;
  status: "scheduled" | "completed" | "cancelled" | "rescheduled";
  createdAt: string;
  updatedAt: string;
  attendance?: { [studentId: string]: SessionAttendanceRecord };
  courseTitle?: string;
  courseSlug?: string;
  courseCategory?: string;
  courseCategories?: string[];
  courseImage?: string;
  coursePrice?: string;
  courseAgeRange?: string;
}

export interface SessionAttendanceRecord {
  studentId: string;
  studentName: string;
  studentEmail: string;
  status: "present" | "absent" | "late" | "excused";
  notes?: string;
  markedAt?: string;
  markedBy?: string;
}

export interface Course {
  id: string;
  slug: string;
  title: string;
  description: string;
  startDate: string;
  schedule: {
    daysOfWeek: string[];
    time: string;
    duration: number;
  };
  price: string;
  outcomes: string[];
  ageRange: string;
  maxStudents: number;
  status: "active" | "archived" | "draft";
  createdAt: string;
  updatedAt: string;
  category: string;
  categories?: string[];
  tags: string[];
  displayOrder: number;
  instructorId?: string;
  location?: string;
  sessions?: CourseSession[];
}

export interface CourseCardProps {
  title: string;
  description: string;
  price: string;
  startDate: string;
  schedule: {
    daysOfWeek: string[];
    time: string;
    duration: number;
  };
  slug: string;
  category?: string;
  categories?: string[];
}

export interface CalendarCardProps {
  course: Course;
}

export interface UseCoursesReturn {
  courses: Course[];
  loading: boolean;
  error: string | null;
  getCourseBySlug: (slug: string) => Course | undefined;
}

// Error boundary types
export interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

export interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error?: Error; resetError: () => void }>;
}

// Loading states
export interface LoadingSpinnerProps {
  size?: "small" | "medium" | "large";
  color?: string;
}

export interface LoadingSkeletonProps {
  width?: string | number;
  height?: string | number;
  className?: string;
}
