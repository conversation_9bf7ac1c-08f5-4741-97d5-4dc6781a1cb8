// Firebase service for AdminBuddy data management
import {
  collection,
  doc,
  getDoc,
  getDocs,
  setDoc,
  updateDoc,
  deleteDoc,
  addDoc,
  query,
  where,
  orderBy,
  limit,
  onSnapshot,
  Timestamp,
  writeBatch,
} from "firebase/firestore";
import { getFunctions, httpsCallable } from "firebase/functions";
import { db, auth } from "../config/firebase";

// Collection names
export const ADMIN_BUDDY_COLLECTIONS = {
  LOCATIONS: "adminbuddy_locations",
  ROUTINES: "adminbuddy_routines",
  ROUTINE_TASKS: "adminbuddy_routine_tasks", // Template tasks that belong to routines
  LOCATION_ROUTINE_SCHEDULES: "adminbuddy_location_routine_schedules", // When routines run at each location
  DAILY_TASKS: "adminbuddy_daily_tasks", // Actual task instances for specific days
  AD_HOC_TASKS: "adminbuddy_ad_hoc_tasks", // One-off tasks not part of routines
  TENANT_ROLES: "adminbuddy_tenant_roles", // Tenant-specific roles
  LOCATION_REPORTS: "adminbuddy_location_reports", // Flexible reporting system
  TASK_COMPLETIONS: "adminbuddy_task_completions",
  // Legacy collections for backward compatibility
  TASKS: "adminbuddy_tasks",
  LOCATION_ROUTINES: "adminbuddy_location_routines",
} as const;

// Types
export interface Location {
  id: string;
  tenantId: string; // SaaS tenant isolation
  name: string;
  address: string;
  createdAt: string;
}

export interface Routine {
  id: string;
  tenantId: string; // SaaS tenant isolation
  name: string;
  description?: string;
  createdAt: string;
}

// Template tasks that belong to routines
export interface RoutineTask {
  id: string;
  tenantId: string; // SaaS tenant isolation
  routineId: string;
  title: string;
  description?: string;
  priority: "low" | "medium" | "high";
  requiredRole?: string; // Tenant-specific role ID
  dueTime?: string; // Relative time like "08:00" or "+30min" after routine start
  expiryType: "next_report" | "set_datetime" | "no_expiry";
  expiryDateTime?: string; // ISO datetime string, only used if expiryType is "set_datetime"
  createdAt: string;
}

// Schedule for when routines run at specific locations
export interface LocationRoutineSchedule {
  id: string;
  tenantId: string; // SaaS tenant isolation
  locationId: string;
  routineId: string;
  daysOfWeek: number[]; // 0=Sunday, 1=Monday, etc.
  startTime?: string; // When tasks are generated (defaults to midnight if blank)
  createdAt: string;
}

// Tenant-specific roles
export interface TenantRole {
  id: string;
  tenantId: string;
  name: string; // e.g., "Keyholder", "Manager", "Barista", "Cashier"
  description?: string;
  permissions: string[]; // Array of permission strings
  createdAt: string;
}

// Actual task instances created for specific days
export interface DailyTask {
  id: string;
  tenantId: string; // SaaS tenant isolation
  routineTaskId: string;
  locationId: string;
  date: string; // YYYY-MM-DD format
  title: string;
  description?: string;
  priority: "low" | "medium" | "high";
  requiredRole?: string; // Tenant-specific role ID
  dueTime?: string;
  expiryType: "next_report" | "set_datetime" | "no_expiry";
  expiryDateTime?: string; // ISO datetime string
  status: "pending" | "completed" | "expired";
  notes?: string; // Task completion notes
  completedAt?: string; // ISO datetime string when task was completed
  reportedAt?: string; // ISO datetime string when task was included in a report
  createdAt: string;
}

// One-off tasks not part of routines
export interface AdHocTask {
  id: string;
  tenantId: string; // SaaS tenant isolation
  locationId: string;
  title: string;
  description?: string;
  priority: "low" | "medium" | "high";
  requiredRole?: string; // Tenant-specific role ID
  dueDate: string; // YYYY-MM-DD format
  dueTime?: string;
  expiryType: "next_report" | "set_datetime" | "no_expiry";
  expiryDateTime?: string; // ISO datetime string
  status: "pending" | "completed" | "expired";
  notes?: string; // Task completion notes
  completedAt?: string; // ISO datetime string when task was completed
  reportedAt?: string; // ISO datetime string when task was included in a report
  createdAt: string;
}

export interface TaskCompletion {
  id: string;
  taskId: string;
  locationId: string;
  completedBy: string;
  completedAt: string;
  notes?: string;
}

// Legacy types for backward compatibility
export interface Task {
  id: string;
  tenantId: string; // SaaS tenant isolation
  title: string;
  description?: string;
  priority: "low" | "medium" | "high";
  routineId?: string;
  estimatedMinutes?: number;
  requiredRole?: string; // Tenant-specific role ID
  dueTime?: string;
  createdAt: string;
}

export interface LocationRoutine {
  id: string;
  tenantId: string; // SaaS tenant isolation
  locationId: string;
  routineId: string;
}

// Tenant Role Services
export const createTenantRole = async (
  roleData: Omit<TenantRole, "id" | "createdAt">
): Promise<string> => {
  try {
    console.log("👥 Creating tenant role:", roleData.name);
    console.log("🚀 Using Firestore REST API...");

    const { createDocument } = await import("./firestoreRestApi");

    const cleanData = Object.fromEntries(
      Object.entries({
        ...roleData,
        createdAt: new Date().toISOString(),
      }).filter(([_, value]) => value !== undefined)
    );

    const roleId = await createDocument(
      ADMIN_BUDDY_COLLECTIONS.TENANT_ROLES,
      cleanData
    );

    console.log("✅ Tenant role created with ID:", roleId);
    return roleId;
  } catch (error) {
    console.error("❌ Error creating tenant role:", error);
    throw error;
  }
};

export const getTenantRoles = async (
  tenantId: string
): Promise<TenantRole[]> => {
  try {
    console.log("👥 Fetching tenant roles for:", tenantId);
    console.log("🚀 Using Firestore REST API...");

    const { queryDocuments } = await import("./firestoreRestApi");

    // Build filters
    const filters = [];
    if (tenantId) {
      filters.push({
        field: "tenantId",
        operator: "==" as const,
        value: tenantId,
      });
    }

    // Query tenant roles with tenant filter only (no ordering to avoid composite index)
    const roles = (await queryDocuments(
      ADMIN_BUDDY_COLLECTIONS.TENANT_ROLES,
      filters
    )) as TenantRole[];

    // Sort in memory by createdAt descending
    roles.sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

    console.log(`✅ Fetched ${roles.length} tenant roles via REST API`);
    return roles;
  } catch (error) {
    console.error("❌ Error fetching tenant roles:", error);
    throw error;
  }
};

export const updateTenantRole = async (
  roleId: string,
  updates: Partial<Omit<TenantRole, "id" | "createdAt">>
): Promise<void> => {
  try {
    console.log("📝 Updating tenant role:", roleId);
    console.log("🚀 Using Firestore REST API...");

    const { updateDocument } = await import("./firestoreRestApi");

    const cleanUpdates = Object.fromEntries(
      Object.entries(updates).filter(([_, value]) => value !== undefined)
    );

    await updateDocument(
      ADMIN_BUDDY_COLLECTIONS.TENANT_ROLES,
      roleId,
      cleanUpdates
    );
    console.log("✅ Tenant role updated successfully");
  } catch (error) {
    console.error("❌ Error updating tenant role:", error);
    throw error;
  }
};

export const deleteTenantRole = async (roleId: string): Promise<void> => {
  try {
    console.log("🗑️ Deleting tenant role:", roleId);
    console.log("🚀 Using Firestore REST API...");

    const { deleteDocument } = await import("./firestoreRestApi");

    await deleteDocument(ADMIN_BUDDY_COLLECTIONS.TENANT_ROLES, roleId);
    console.log("✅ Tenant role deleted successfully");
  } catch (error) {
    console.error("❌ Error deleting tenant role:", error);
    throw error;
  }
};

export const createDefaultTenantRoles = async (
  tenantId: string
): Promise<void> => {
  try {
    console.log("👥 Creating default roles for tenant:", tenantId);

    const defaultRoles = [
      {
        tenantId,
        name: "Any Employee",
        description: "General tasks that any team member can complete",
        permissions: ["complete_tasks", "view_tasks"], // Keep for backend compatibility
      },
      {
        tenantId,
        name: "Keyholder",
        description: "Opening/closing tasks and secure operations",
        permissions: ["complete_tasks", "view_tasks", "key_access"], // Keep for backend compatibility
      },
      {
        tenantId,
        name: "Manager",
        description: "Management and supervisory tasks",
        permissions: [
          "complete_tasks",
          "view_tasks",
          "key_access",
          "manage_tasks",
        ], // Keep for backend compatibility
      },
    ];

    for (const role of defaultRoles) {
      await createTenantRole(role);
    }

    console.log("✅ Default tenant roles created");
  } catch (error) {
    console.error("❌ Error creating default tenant roles:", error);
    throw error;
  }
};

// Location Services
export const createLocation = async (
  locationData: Omit<Location, "id" | "createdAt">
): Promise<string> => {
  try {
    console.log("🏢 Creating location:", locationData.name);
    console.log("🚀 Using Firestore REST API...");

    // Import REST API helper
    const { createDocument } = await import("./firestoreRestApi");

    // Create location with timestamp
    const locationId = await createDocument(ADMIN_BUDDY_COLLECTIONS.LOCATIONS, {
      ...locationData,
      createdAt: new Date().toISOString(),
    });

    console.log("✅ Location created with ID:", locationId);
    return locationId;
  } catch (error) {
    console.error("❌ Error creating location:", error);
    throw error;
  }
};

export const updateLocation = async (
  locationId: string,
  updates: Partial<Omit<Location, "id" | "createdAt">>
): Promise<void> => {
  try {
    console.log("📝 Updating location:", locationId);
    console.log("🚀 Using Firestore REST API...");

    const { updateDocument } = await import("./firestoreRestApi");

    const cleanUpdates = Object.fromEntries(
      Object.entries(updates).filter(([_, value]) => value !== undefined)
    );

    await updateDocument(
      ADMIN_BUDDY_COLLECTIONS.LOCATIONS,
      locationId,
      cleanUpdates
    );
    console.log("✅ Location updated successfully");
  } catch (error) {
    console.error("❌ Error updating location:", error);
    throw error;
  }
};

export const deleteLocation = async (locationId: string): Promise<void> => {
  try {
    console.log("🗑️ Deleting location:", locationId);
    console.log("🔄 This will cascade delete all associated data...");

    // Use REST API for better batch handling
    const { deleteDocument, queryDocuments } = await import(
      "./firestoreRestApi"
    );

    // 1. Delete location
    console.log("🗑️ Deleting location document...");
    await deleteDocument(ADMIN_BUDDY_COLLECTIONS.LOCATIONS, locationId);

    // 2. Delete associated schedules
    console.log("🗑️ Deleting location routine schedules...");
    const schedules = await queryDocuments(
      ADMIN_BUDDY_COLLECTIONS.LOCATION_ROUTINE_SCHEDULES,
      [{ field: "locationId", operator: "==", value: locationId }]
    );
    for (const schedule of schedules) {
      await deleteDocument(
        ADMIN_BUDDY_COLLECTIONS.LOCATION_ROUTINE_SCHEDULES,
        schedule.id
      );
    }

    // 3. Delete associated daily tasks
    console.log("🗑️ Deleting daily tasks for location...");
    const dailyTasks = await queryDocuments(
      ADMIN_BUDDY_COLLECTIONS.DAILY_TASKS,
      [{ field: "locationId", operator: "==", value: locationId }]
    );
    for (const task of dailyTasks) {
      await deleteDocument(ADMIN_BUDDY_COLLECTIONS.DAILY_TASKS, task.id);
    }

    // 4. Delete associated ad-hoc tasks
    console.log("🗑️ Deleting ad-hoc tasks for location...");
    const adHocTasks = await queryDocuments(
      ADMIN_BUDDY_COLLECTIONS.AD_HOC_TASKS,
      [{ field: "locationId", operator: "==", value: locationId }]
    );
    for (const task of adHocTasks) {
      await deleteDocument(ADMIN_BUDDY_COLLECTIONS.AD_HOC_TASKS, task.id);
    }

    console.log(
      `✅ Location and all related data deleted successfully (${schedules.length} schedules, ${dailyTasks.length} daily tasks, ${adHocTasks.length} ad-hoc tasks)`
    );
  } catch (error) {
    console.error("❌ Error deleting location:", error);
    throw error;
  }
};

export const getAllLocations = async (
  tenantId?: string
): Promise<Location[]> => {
  try {
    console.log(
      "📋 Fetching all locations",
      tenantId ? `for tenant: ${tenantId}` : "(WARNING: No tenant filter!)"
    );
    console.log("🚀 Using Firestore REST API...");

    // Import REST API helper
    const { queryDocuments } = await import("./firestoreRestApi");

    // Build filters
    const filters = [];
    if (tenantId) {
      filters.push({
        field: "tenantId",
        operator: "==" as const,
        value: tenantId,
      });
    }

    // Query locations with tenant filter and ordering
    const locations = (await queryDocuments(
      ADMIN_BUDDY_COLLECTIONS.LOCATIONS,
      filters,
      [{ field: "createdAt", direction: "desc" }]
    )) as Location[];

    console.log(`✅ Fetched ${locations.length} locations via REST API`);
    return locations;
  } catch (error) {
    console.error("❌ Error fetching locations:", error);
    throw error;
  }
};

// Routine Services
export const createRoutine = async (
  routineData: Omit<Routine, "id" | "createdAt">
): Promise<string> => {
  try {
    console.log("🔄 Creating routine:", routineData.name);
    console.log("🚀 Using Firestore REST API...");

    // Import REST API helper
    const { createDocument } = await import("./firestoreRestApi");

    // Create routine with timestamp
    const routineId = await createDocument(ADMIN_BUDDY_COLLECTIONS.ROUTINES, {
      ...routineData,
      createdAt: new Date().toISOString(),
    });

    console.log("✅ Routine created with ID:", routineId);
    return routineId;
  } catch (error) {
    console.error("❌ Error creating routine:", error);
    throw error;
  }
};

export const updateRoutine = async (
  routineId: string,
  updates: Partial<Omit<Routine, "id" | "createdAt">>
): Promise<void> => {
  try {
    console.log("📝 Updating routine:", routineId);
    console.log("🚀 Using Firestore REST API...");

    const { updateDocument } = await import("./firestoreRestApi");

    const cleanUpdates = Object.fromEntries(
      Object.entries(updates).filter(([_, value]) => value !== undefined)
    );

    await updateDocument(
      ADMIN_BUDDY_COLLECTIONS.ROUTINES,
      routineId,
      cleanUpdates
    );
    console.log("✅ Routine updated successfully");
  } catch (error) {
    console.error("❌ Error updating routine:", error);
    throw error;
  }
};

export const deleteRoutine = async (routineId: string): Promise<void> => {
  try {
    console.log("🗑️ Deleting routine:", routineId);
    console.log("🔄 This will cascade delete all associated data...");

    // Use REST API for better batch handling
    const { deleteDocument, queryDocuments } = await import(
      "./firestoreRestApi"
    );

    // 1. Delete routine
    console.log("🗑️ Deleting routine document...");
    await deleteDocument(ADMIN_BUDDY_COLLECTIONS.ROUTINES, routineId);

    // 2. Delete associated routine tasks
    console.log("🗑️ Deleting routine tasks...");
    const routineTasks = await queryDocuments(
      ADMIN_BUDDY_COLLECTIONS.ROUTINE_TASKS,
      [{ field: "routineId", operator: "==", value: routineId }]
    );
    for (const task of routineTasks) {
      await deleteDocument(ADMIN_BUDDY_COLLECTIONS.ROUTINE_TASKS, task.id);
    }

    // 3. Delete associated schedules
    console.log("🗑️ Deleting location routine schedules...");
    const schedules = await queryDocuments(
      ADMIN_BUDDY_COLLECTIONS.LOCATION_ROUTINE_SCHEDULES,
      [{ field: "routineId", operator: "==", value: routineId }]
    );
    for (const schedule of schedules) {
      await deleteDocument(
        ADMIN_BUDDY_COLLECTIONS.LOCATION_ROUTINE_SCHEDULES,
        schedule.id
      );
    }

    // 4. Delete associated daily tasks (generated from this routine's tasks)
    console.log("🗑️ Deleting daily tasks generated from routine tasks...");
    let dailyTasksDeleted = 0;
    for (const routineTask of routineTasks) {
      const dailyTasks = await queryDocuments(
        ADMIN_BUDDY_COLLECTIONS.DAILY_TASKS,
        [{ field: "routineTaskId", operator: "==", value: routineTask.id }]
      );
      for (const task of dailyTasks) {
        await deleteDocument(ADMIN_BUDDY_COLLECTIONS.DAILY_TASKS, task.id);
        dailyTasksDeleted++;
      }
    }

    console.log(
      `✅ Routine and all related data deleted successfully (${routineTasks.length} routine tasks, ${schedules.length} schedules, ${dailyTasksDeleted} daily tasks)`
    );
  } catch (error) {
    console.error("❌ Error deleting routine:", error);
    throw error;
  }
};

export const getAllRoutines = async (tenantId?: string): Promise<Routine[]> => {
  try {
    console.log(
      "📋 Fetching all routines",
      tenantId ? `for tenant: ${tenantId}` : ""
    );
    console.log("🚀 Using Firestore REST API...");

    const { queryDocuments } = await import("./firestoreRestApi");

    // Build filters
    const filters = [];
    if (tenantId) {
      filters.push({
        field: "tenantId",
        operator: "==" as const,
        value: tenantId,
      });
    }

    // Query routines with tenant filter and ordering
    const routines = (await queryDocuments(
      ADMIN_BUDDY_COLLECTIONS.ROUTINES,
      filters,
      [{ field: "createdAt", direction: "desc" }]
    )) as Routine[];

    console.log(`✅ Fetched ${routines.length} routines via REST API`);
    return routines;
  } catch (error) {
    console.error("❌ Error fetching routines:", error);
    throw error;
  }
};

// Task Services
export const createTask = async (
  taskData: Omit<Task, "id" | "createdAt">
): Promise<string> => {
  try {
    console.log("✅ Creating task:", taskData.title);
    const tasksRef = collection(db, ADMIN_BUDDY_COLLECTIONS.TASKS);

    // Clean the data to remove undefined values (Firebase doesn't allow undefined)
    const cleanTaskData = Object.fromEntries(
      Object.entries({
        ...taskData,
        createdAt: new Date().toISOString(),
      }).filter(([_, value]) => value !== undefined)
    );

    const docRef = await addDoc(tasksRef, cleanTaskData);
    console.log("✅ Task created with ID:", docRef.id);
    return docRef.id;
  } catch (error) {
    console.error("❌ Error creating task:", error);
    throw error;
  }
};

export const updateTask = async (
  taskId: string,
  updates: Partial<Omit<Task, "id" | "createdAt">>
): Promise<void> => {
  try {
    console.log("📝 Updating task:", taskId);
    const taskRef = doc(db, ADMIN_BUDDY_COLLECTIONS.TASKS, taskId);

    // Clean the updates to remove undefined values
    const cleanUpdates = Object.fromEntries(
      Object.entries(updates).filter(([_, value]) => value !== undefined)
    );

    await updateDoc(taskRef, cleanUpdates);
    console.log("✅ Task updated successfully");
  } catch (error) {
    console.error("❌ Error updating task:", error);
    throw error;
  }
};

export const deleteTask = async (taskId: string): Promise<void> => {
  try {
    console.log("🗑️ Deleting task:", taskId);
    const taskRef = doc(db, ADMIN_BUDDY_COLLECTIONS.TASKS, taskId);
    await deleteDoc(taskRef);
    console.log("✅ Task deleted successfully");
  } catch (error) {
    console.error("❌ Error deleting task:", error);
    throw error;
  }
};

export const getAllTasks = async (): Promise<Task[]> => {
  try {
    console.log("📋 Fetching all tasks");
    const tasksRef = collection(db, ADMIN_BUDDY_COLLECTIONS.TASKS);
    const q = query(tasksRef, orderBy("createdAt", "desc"));
    const snapshot = await getDocs(q);

    const tasks = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as Task[];

    console.log(`✅ Fetched ${tasks.length} tasks`);
    return tasks;
  } catch (error) {
    console.error("❌ Error fetching tasks:", error);
    throw error;
  }
};

// Location-Routine Services
export const getAllLocationRoutines = async (): Promise<LocationRoutine[]> => {
  try {
    console.log("📋 Fetching all location-routine relationships");
    const locationRoutinesRef = collection(
      db,
      ADMIN_BUDDY_COLLECTIONS.LOCATION_ROUTINES
    );
    const snapshot = await getDocs(locationRoutinesRef);

    const locationRoutines = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as LocationRoutine[];

    console.log(
      `✅ Fetched ${locationRoutines.length} location-routine relationships`
    );
    return locationRoutines;
  } catch (error) {
    console.error("❌ Error fetching location-routine relationships:", error);
    throw error;
  }
};

// Routine Task Services
export const createRoutineTask = async (
  routineTaskData: Omit<RoutineTask, "id" | "createdAt">
): Promise<string> => {
  try {
    console.log("📋 Creating routine task:", routineTaskData.title);
    console.log("🚀 Using Firestore REST API...");

    const { createDocument } = await import("./firestoreRestApi");

    const cleanData = Object.fromEntries(
      Object.entries({
        ...routineTaskData,
        createdAt: new Date().toISOString(),
      }).filter(([_, value]) => value !== undefined)
    );

    const routineTaskId = await createDocument(
      ADMIN_BUDDY_COLLECTIONS.ROUTINE_TASKS,
      cleanData
    );

    console.log("✅ Routine task created with ID:", routineTaskId);
    return routineTaskId;
  } catch (error) {
    console.error("❌ Error creating routine task:", error);
    throw error;
  }
};

export const getAllRoutineTasks = async (
  tenantId?: string
): Promise<RoutineTask[]> => {
  try {
    console.log(
      "📋 Fetching all routine tasks",
      tenantId ? `for tenant: ${tenantId}` : ""
    );
    console.log("🚀 Using Firestore REST API...");

    const { queryDocuments } = await import("./firestoreRestApi");

    // Build filters
    const filters = [];
    if (tenantId) {
      filters.push({
        field: "tenantId",
        operator: "==" as const,
        value: tenantId,
      });
    }

    // Query routine tasks with tenant filter and ordering
    const routineTasks = (await queryDocuments(
      ADMIN_BUDDY_COLLECTIONS.ROUTINE_TASKS,
      filters,
      [
        { field: "order", direction: "asc" },
        { field: "createdAt", direction: "desc" },
      ]
    )) as RoutineTask[];

    console.log(`✅ Fetched ${routineTasks.length} routine tasks via REST API`);
    return routineTasks;
  } catch (error) {
    console.error("❌ Error fetching routine tasks:", error);
    throw error;
  }
};

export const updateRoutineTask = async (
  routineTaskId: string,
  updates: Partial<Omit<RoutineTask, "id" | "createdAt">>
): Promise<void> => {
  try {
    console.log("📝 Updating routine task:", routineTaskId);
    console.log("🚀 Using Firestore REST API...");

    const { updateDocument } = await import("./firestoreRestApi");

    const cleanUpdates = Object.fromEntries(
      Object.entries(updates).filter(([_, value]) => value !== undefined)
    );

    await updateDocument(
      ADMIN_BUDDY_COLLECTIONS.ROUTINE_TASKS,
      routineTaskId,
      cleanUpdates
    );
    console.log("✅ Routine task updated successfully");
  } catch (error) {
    console.error("❌ Error updating routine task:", error);
    throw error;
  }
};

export const deleteRoutineTask = async (
  routineTaskId: string
): Promise<void> => {
  try {
    console.log("🗑️ Deleting routine task:", routineTaskId);
    console.log("🔄 This will cascade delete all associated daily tasks...");

    // Use REST API for better batch handling
    const { deleteDocument, queryDocuments } = await import(
      "./firestoreRestApi"
    );

    // 1. Delete associated daily tasks first
    console.log("🗑️ Deleting daily tasks generated from this routine task...");
    const dailyTasks = await queryDocuments(
      ADMIN_BUDDY_COLLECTIONS.DAILY_TASKS,
      [{ field: "routineTaskId", operator: "==", value: routineTaskId }]
    );
    for (const task of dailyTasks) {
      await deleteDocument(ADMIN_BUDDY_COLLECTIONS.DAILY_TASKS, task.id);
    }

    // 2. Delete routine task
    console.log("🗑️ Deleting routine task document...");
    await deleteDocument(ADMIN_BUDDY_COLLECTIONS.ROUTINE_TASKS, routineTaskId);

    console.log(
      `✅ Routine task and ${dailyTasks.length} associated daily tasks deleted successfully`
    );
  } catch (error) {
    console.error("❌ Error deleting routine task:", error);
    throw error;
  }
};

// Location Routine Schedule Services
export const createLocationRoutineSchedule = async (
  scheduleData: Omit<LocationRoutineSchedule, "id" | "createdAt">
): Promise<string> => {
  try {
    console.log("📅 Creating location routine schedule");
    console.log("🚀 Using Firestore REST API...");

    const { createDocument } = await import("./firestoreRestApi");

    const cleanData = Object.fromEntries(
      Object.entries({
        ...scheduleData,
        createdAt: new Date().toISOString(),
      }).filter(([_, value]) => value !== undefined)
    );

    const scheduleId = await createDocument(
      ADMIN_BUDDY_COLLECTIONS.LOCATION_ROUTINE_SCHEDULES,
      cleanData
    );

    console.log("✅ Location routine schedule created with ID:", scheduleId);
    return scheduleId;
  } catch (error) {
    console.error("❌ Error creating location routine schedule:", error);
    throw error;
  }
};

export const getAllLocationRoutineSchedules = async (
  tenantId?: string
): Promise<LocationRoutineSchedule[]> => {
  try {
    console.log(
      "📅 Fetching all location routine schedules",
      tenantId ? `for tenant: ${tenantId}` : ""
    );
    console.log("🚀 Using Firestore REST API...");

    const { queryDocuments } = await import("./firestoreRestApi");

    // Build filters
    const filters = [];
    if (tenantId) {
      filters.push({
        field: "tenantId",
        operator: "==" as const,
        value: tenantId,
      });
    }

    // Query schedules with tenant filter and ordering
    const schedules = (await queryDocuments(
      ADMIN_BUDDY_COLLECTIONS.LOCATION_ROUTINE_SCHEDULES,
      filters,
      [{ field: "createdAt", direction: "desc" }]
    )) as LocationRoutineSchedule[];

    console.log(
      `✅ Fetched ${schedules.length} location routine schedules via REST API`
    );
    return schedules;
  } catch (error) {
    console.error("❌ Error fetching location routine schedules:", error);
    throw error;
  }
};

export const updateLocationRoutineSchedule = async (
  scheduleId: string,
  updates: Partial<Omit<LocationRoutineSchedule, "id" | "createdAt">>
): Promise<void> => {
  try {
    console.log("📝 Updating location routine schedule:", scheduleId);
    console.log("🚀 Using Firestore REST API...");

    const { updateDocument } = await import("./firestoreRestApi");

    const cleanUpdates = Object.fromEntries(
      Object.entries(updates).filter(([_, value]) => value !== undefined)
    );

    await updateDocument(
      ADMIN_BUDDY_COLLECTIONS.LOCATION_ROUTINE_SCHEDULES,
      scheduleId,
      cleanUpdates
    );
    console.log("✅ Location routine schedule updated successfully");
  } catch (error) {
    console.error("❌ Error updating location routine schedule:", error);
    throw error;
  }
};

export const deleteLocationRoutineSchedule = async (
  scheduleId: string
): Promise<void> => {
  try {
    console.log("🗑️ Deleting location routine schedule:", scheduleId);
    const scheduleRef = doc(
      db,
      ADMIN_BUDDY_COLLECTIONS.LOCATION_ROUTINE_SCHEDULES,
      scheduleId
    );
    await deleteDoc(scheduleRef);
    console.log("✅ Location routine schedule deleted successfully");
  } catch (error) {
    console.error("❌ Error deleting location routine schedule:", error);
    throw error;
  }
};

// Kiosk-specific services
export const getTasksForLocation = async (
  locationId: string
): Promise<Task[]> => {
  try {
    console.log("📋 Fetching tasks for location:", locationId);

    // Get all active tasks
    const tasksRef = collection(db, ADMIN_BUDDY_COLLECTIONS.TASKS);
    const tasksQuery = query(tasksRef, where("isActive", "==", true));
    const tasksSnapshot = await getDocs(tasksQuery);

    const allTasks = tasksSnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as Task[];

    // Get location-routine relationships for this location
    const locationRoutinesRef = collection(
      db,
      ADMIN_BUDDY_COLLECTIONS.LOCATION_ROUTINES
    );
    const locationRoutinesQuery = query(
      locationRoutinesRef,
      where("locationId", "==", locationId),
      where("isActive", "==", true)
    );
    const locationRoutinesSnapshot = await getDocs(locationRoutinesQuery);

    const locationRoutineIds = locationRoutinesSnapshot.docs.map(
      (doc) => doc.data().routineId
    );

    // Filter tasks to only include those for this location's routines or ad-hoc tasks
    const locationTasks = allTasks.filter(
      (task) => !task.routineId || locationRoutineIds.includes(task.routineId)
    );

    console.log(
      `✅ Fetched ${locationTasks.length} tasks for location ${locationId}`
    );
    return locationTasks;
  } catch (error) {
    console.error("❌ Error fetching tasks for location:", error);
    throw error;
  }
};

export const getRoutinesForLocation = async (
  locationId: string
): Promise<Routine[]> => {
  try {
    console.log("📋 Fetching routines for location:", locationId);

    // Get location-routine relationships for this location
    const locationRoutinesRef = collection(
      db,
      ADMIN_BUDDY_COLLECTIONS.LOCATION_ROUTINES
    );
    const locationRoutinesQuery = query(
      locationRoutinesRef,
      where("locationId", "==", locationId),
      where("isActive", "==", true)
    );
    const locationRoutinesSnapshot = await getDocs(locationRoutinesQuery);

    const routineIds = locationRoutinesSnapshot.docs.map(
      (doc) => doc.data().routineId
    );

    if (routineIds.length === 0) {
      console.log("✅ No routines found for location");
      return [];
    }

    // Get the actual routine documents
    const routinesRef = collection(db, ADMIN_BUDDY_COLLECTIONS.ROUTINES);
    const routinesQuery = query(routinesRef, where("isActive", "==", true));
    const routinesSnapshot = await getDocs(routinesQuery);

    const allRoutines = routinesSnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as Routine[];

    // Filter to only routines for this location
    const locationRoutines = allRoutines.filter((routine) =>
      routineIds.includes(routine.id)
    );

    console.log(
      `✅ Fetched ${locationRoutines.length} routines for location ${locationId}`
    );
    return locationRoutines;
  } catch (error) {
    console.error("❌ Error fetching routines for location:", error);
    throw error;
  }
};

// Daily Task Generation Services
export const generateDailyTasksForDate = async (
  tenantId: string,
  date: string // YYYY-MM-DD format
): Promise<void> => {
  try {
    console.log(`📅 Generating daily tasks for ${date} (tenant: ${tenantId})`);

    // Get all active schedules for this tenant
    const schedulesRef = collection(
      db,
      ADMIN_BUDDY_COLLECTIONS.LOCATION_ROUTINE_SCHEDULES
    );
    const schedulesQuery = query(
      schedulesRef,
      where("tenantId", "==", tenantId),
      where("isActive", "==", true)
    );
    const schedulesSnapshot = await getDocs(schedulesQuery);

    if (schedulesSnapshot.empty) {
      console.log("⚠️ No active schedules found for tenant");
      return;
    }

    // Get day of week for the date (0=Sunday, 1=Monday, etc.)
    const dateObj = new Date(date + "T00:00:00");
    const dayOfWeek = dateObj.getDay();

    // Get all routine tasks for this tenant
    const routineTasksRef = collection(
      db,
      ADMIN_BUDDY_COLLECTIONS.ROUTINE_TASKS
    );
    const routineTasksQuery = query(
      routineTasksRef,
      where("tenantId", "==", tenantId),
      where("isActive", "==", true)
    );
    const routineTasksSnapshot = await getDocs(routineTasksQuery);
    const routineTasks = routineTasksSnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as RoutineTask[];

    let tasksCreated = 0;

    // Process each schedule
    for (const scheduleDoc of schedulesSnapshot.docs) {
      const schedule = {
        id: scheduleDoc.id,
        ...scheduleDoc.data(),
      } as LocationRoutineSchedule;

      // Check if this schedule runs on this day of week
      if (!schedule.daysOfWeek.includes(dayOfWeek)) {
        continue;
      }

      // Get routine tasks for this routine
      const routineTasksForSchedule = routineTasks.filter(
        (rt) => rt.routineId === schedule.routineId
      );

      // Create daily tasks for each routine task
      for (const routineTask of routineTasksForSchedule) {
        // Check if task already exists for this date
        const existingTaskQuery = query(
          collection(db, ADMIN_BUDDY_COLLECTIONS.DAILY_TASKS),
          where("tenantId", "==", tenantId),
          where("routineTaskId", "==", routineTask.id),
          where("locationId", "==", schedule.locationId),
          where("date", "==", date)
        );
        const existingTaskSnapshot = await getDocs(existingTaskQuery);

        if (!existingTaskSnapshot.empty) {
          console.log(
            `⏭️ Task already exists: ${routineTask.title} at location ${schedule.locationId}`
          );
          continue;
        }

        // Calculate due time and expiry
        let dueTime = routineTask.dueTime;
        let expiryDateTime = routineTask.expiryDateTime;

        // If routine has start time and task has relative time, calculate absolute time
        if (schedule.startTime && routineTask.dueTime?.startsWith("+")) {
          // Handle relative time like "+30min"
          const minutes = parseInt(
            routineTask.dueTime.replace("+", "").replace("min", "")
          );
          const [startHour, startMinute] = schedule.startTime
            .split(":")
            .map(Number);
          const totalMinutes = startHour * 60 + startMinute + minutes;
          const newHour = Math.floor(totalMinutes / 60);
          const newMinute = totalMinutes % 60;
          dueTime = `${newHour.toString().padStart(2, "0")}:${newMinute
            .toString()
            .padStart(2, "0")}`;
        }

        // Handle expiry date calculation
        if (
          routineTask.expiryType === "set_datetime" &&
          !routineTask.expiryDateTime
        ) {
          // If set_datetime but no specific datetime, default to end of day
          expiryDateTime = `${date}T23:59:59`;
        }

        // Create daily task
        const dailyTask: Omit<DailyTask, "id"> = {
          tenantId,
          routineTaskId: routineTask.id,
          locationId: schedule.locationId,
          date,
          title: routineTask.title,
          description: routineTask.description,
          priority: routineTask.priority,
          requiredRole: routineTask.requiredRole,
          dueTime,
          expiryType: routineTask.expiryType,
          expiryDateTime,
          status: "pending",
          createdAt: new Date().toISOString(),
        };

        await addDoc(
          collection(db, ADMIN_BUDDY_COLLECTIONS.DAILY_TASKS),
          dailyTask
        );
        tasksCreated++;

        console.log(
          `✅ Created daily task: ${routineTask.title} for ${schedule.locationId}`
        );
      }
    }

    console.log(`🎉 Generated ${tasksCreated} daily tasks for ${date}`);
  } catch (error) {
    console.error("❌ Error generating daily tasks:", error);
    throw error;
  }
};

export const generateDailyTasksForToday = async (
  tenantId: string
): Promise<void> => {
  const today = new Date().toISOString().split("T")[0]; // YYYY-MM-DD
  return generateDailyTasksForDate(tenantId, today);
};

export const generateDailyTasksForDateRange = async (
  tenantId: string,
  startDate: string,
  endDate: string
): Promise<void> => {
  const start = new Date(startDate);
  const end = new Date(endDate);

  for (
    let date = new Date(start);
    date <= end;
    date.setDate(date.getDate() + 1)
  ) {
    const dateStr = date.toISOString().split("T")[0];
    await generateDailyTasksForDate(tenantId, dateStr);
  }
};

// Daily Task CRUD Services
export const getAllDailyTasks = async (
  tenantId?: string,
  date?: string,
  locationId?: string
): Promise<DailyTask[]> => {
  try {
    console.log("📋 Fetching daily tasks", {
      tenantId,
      date,
      locationId,
      timestamp: new Date().toISOString(),
    });
    console.log("🚀 Using Firestore REST API...");

    const { queryDocuments } = await import("./firestoreRestApi");

    // Build filters
    const filters = [];
    if (tenantId) {
      filters.push({
        field: "tenantId",
        operator: "==" as const,
        value: tenantId,
      });
    }
    if (date) {
      filters.push({
        field: "date",
        operator: "==" as const,
        value: date,
      });
    }
    if (locationId) {
      filters.push({
        field: "locationId",
        operator: "==" as const,
        value: locationId,
      });
    }

    // Query daily tasks with filters only (temporarily removing ordering to debug)
    // TODO: Re-add ordering once we confirm completed tasks are being returned
    const dailyTasks = (await queryDocuments(
      ADMIN_BUDDY_COLLECTIONS.DAILY_TASKS,
      filters
      // Temporarily removing ordering: [{ field: "createdAt", direction: "desc" }]
    )) as DailyTask[];

    console.log(`✅ Fetched ${dailyTasks.length} daily tasks via REST API`);

    // Debug: Log details about each task
    const pendingTasks = dailyTasks.filter((t) => t.status === "pending");
    const completedTasks = dailyTasks.filter((t) => t.status === "completed");
    const expiredTasks = dailyTasks.filter((t) => t.status === "expired");

    console.log(`📊 Daily tasks status breakdown:`, {
      pending: pendingTasks.length,
      completed: completedTasks.length,
      expired: expiredTasks.length,
      total: dailyTasks.length,
    });

    return dailyTasks;
  } catch (error) {
    console.error("❌ Error fetching daily tasks:", error);
    throw error;
  }
};

export const createDailyTask = async (
  taskData: Omit<DailyTask, "id" | "createdAt">
): Promise<string> => {
  try {
    console.log("📝 Creating daily task:", taskData.title);
    console.log("🚀 Using Firestore REST API...");

    const { createDocument } = await import("./firestoreRestApi");

    const cleanData = Object.fromEntries(
      Object.entries({
        ...taskData,
        createdAt: new Date().toISOString(),
      }).filter(([_, value]) => value !== undefined)
    );

    const taskId = await createDocument(
      ADMIN_BUDDY_COLLECTIONS.DAILY_TASKS,
      cleanData
    );

    console.log("✅ Daily task created with ID:", taskId);
    return taskId;
  } catch (error) {
    console.error("❌ Error creating daily task:", error);
    throw error;
  }
};

export const updateDailyTaskStatus = async (
  taskId: string,
  status: "pending" | "completed" | "expired",
  notes?: string
): Promise<void> => {
  try {
    console.log(`📝 Updating daily task status: ${taskId} -> ${status}`);
    console.log("🚀 Using Firestore REST API...");

    const { updateDocument, getDocument } = await import("./firestoreRestApi");

    // First, get the existing task to retrieve its tenantId
    const existingTask = await getDocument(
      ADMIN_BUDDY_COLLECTIONS.DAILY_TASKS,
      taskId
    );

    if (!existingTask) {
      throw new Error(`Daily task ${taskId} not found`);
    }

    // Preserve ALL existing task properties and only update specific fields
    const updates: any = {
      ...existingTask, // Preserve all existing properties
      status, // Update status
    };
    if (status === "completed") {
      updates.completedAt = new Date().toISOString();
    }
    if (notes) {
      updates.notes = notes;
    }

    const cleanUpdates = Object.fromEntries(
      Object.entries(updates).filter(([_, value]) => value !== undefined)
    );

    await updateDocument(
      ADMIN_BUDDY_COLLECTIONS.DAILY_TASKS,
      taskId,
      cleanUpdates
    );
    console.log("✅ Daily task status updated successfully");

    // Debug: Verify the update by fetching the task again
    const updatedTask = await getDocument(
      ADMIN_BUDDY_COLLECTIONS.DAILY_TASKS,
      taskId
    );
    console.log("🔍 Verification - Task after update:", {
      id: taskId,
      status: updatedTask?.status,
      completedAt: updatedTask?.completedAt,
      title: updatedTask?.title,
    });
  } catch (error) {
    console.error("❌ Error updating daily task status:", error);
    throw error;
  }
};

// Ad-hoc Task CRUD Services
export const createAdHocTask = async (
  taskData: Omit<AdHocTask, "id" | "createdAt">
): Promise<string> => {
  try {
    console.log("⚡ Creating ad-hoc task:", taskData.title);
    console.log("🚀 Using Firestore REST API...");

    const { createDocument } = await import("./firestoreRestApi");

    const cleanData = Object.fromEntries(
      Object.entries({
        ...taskData,
        createdAt: new Date().toISOString(),
      }).filter(([_, value]) => value !== undefined)
    );

    const taskId = await createDocument(
      ADMIN_BUDDY_COLLECTIONS.AD_HOC_TASKS,
      cleanData
    );

    console.log("✅ Ad-hoc task created with ID:", taskId);
    return taskId;
  } catch (error) {
    console.error("❌ Error creating ad-hoc task:", error);
    throw error;
  }
};

export const getAllAdHocTasks = async (
  tenantId?: string,
  locationId?: string,
  status?: "pending" | "completed" | "expired"
): Promise<AdHocTask[]> => {
  try {
    console.log("⚡ Fetching ad-hoc tasks", { tenantId, locationId, status });
    console.log("🚀 Using Firestore REST API...");

    const { queryDocuments } = await import("./firestoreRestApi");

    // Build filters
    const filters = [];
    if (tenantId) {
      filters.push({
        field: "tenantId",
        operator: "==" as const,
        value: tenantId,
      });
    }
    if (locationId) {
      filters.push({
        field: "locationId",
        operator: "==" as const,
        value: locationId,
      });
    }
    if (status) {
      filters.push({
        field: "status",
        operator: "==" as const,
        value: status,
      });
    }

    // Query ad-hoc tasks with filters and ordering (index now available)
    const adHocTasks = (await queryDocuments(
      ADMIN_BUDDY_COLLECTIONS.AD_HOC_TASKS,
      filters,
      [
        { field: "dueDate", direction: "asc" },
        { field: "dueTime", direction: "asc" },
        { field: "createdAt", direction: "desc" },
      ]
    )) as AdHocTask[];

    console.log(`✅ Fetched ${adHocTasks.length} ad-hoc tasks via REST API`);
    return adHocTasks;
  } catch (error) {
    console.error("❌ Error fetching ad-hoc tasks:", error);
    throw error;
  }
};

export const updateAdHocTask = async (
  taskId: string,
  updates: Partial<Omit<AdHocTask, "id" | "createdAt">>
): Promise<void> => {
  try {
    console.log("📝 Updating ad-hoc task:", taskId);
    console.log("🚀 Using Firestore REST API...");

    const { updateDocument } = await import("./firestoreRestApi");

    const cleanUpdates = Object.fromEntries(
      Object.entries(updates).filter(([_, value]) => value !== undefined)
    );

    await updateDocument(
      ADMIN_BUDDY_COLLECTIONS.AD_HOC_TASKS,
      taskId,
      cleanUpdates
    );
    console.log("✅ Ad-hoc task updated successfully");
  } catch (error) {
    console.error("❌ Error updating ad-hoc task:", error);
    throw error;
  }
};

export const updateAdHocTaskStatus = async (
  taskId: string,
  status: "pending" | "completed" | "expired",
  notes?: string
): Promise<void> => {
  try {
    console.log(`📝 Updating ad-hoc task status: ${taskId} -> ${status}`);
    console.log("🚀 Using Firestore REST API...");

    const { updateDocument, getDocument } = await import("./firestoreRestApi");

    // First, get the existing task to retrieve its tenantId
    const existingTask = await getDocument(
      ADMIN_BUDDY_COLLECTIONS.AD_HOC_TASKS,
      taskId
    );

    if (!existingTask) {
      throw new Error(`Ad-hoc task ${taskId} not found`);
    }

    // Preserve ALL existing task properties and only update specific fields
    const updates: any = {
      ...existingTask, // Preserve all existing properties
      status, // Update status
    };
    if (status === "completed") {
      updates.completedAt = new Date().toISOString();
    }
    if (notes) {
      updates.notes = notes;
    }

    const cleanUpdates = Object.fromEntries(
      Object.entries(updates).filter(([_, value]) => value !== undefined)
    );

    await updateDocument(
      ADMIN_BUDDY_COLLECTIONS.AD_HOC_TASKS,
      taskId,
      cleanUpdates
    );
    console.log("✅ Ad-hoc task status updated successfully");
  } catch (error) {
    console.error("❌ Error updating ad-hoc task status:", error);
    throw error;
  }
};

export const deleteAdHocTask = async (taskId: string): Promise<void> => {
  try {
    console.log("🗑️ Deleting ad-hoc task:", taskId);
    const taskRef = doc(db, ADMIN_BUDDY_COLLECTIONS.AD_HOC_TASKS, taskId);
    await deleteDoc(taskRef);
    console.log("✅ Ad-hoc task deleted successfully");
  } catch (error) {
    console.error("❌ Error deleting ad-hoc task:", error);
    throw error;
  }
};

// Cloud Functions Services
const functions = getFunctions();

// Token refresh function (copied from TenantContext for standalone use)
const refreshAuthToken = async (
  refreshToken: string
): Promise<{
  idToken: string;
  refreshToken: string;
  expiresIn: string;
} | null> => {
  try {
    console.log("🔄 Refreshing auth token...");
    const apiKey = "AIzaSyCDtTEuXAROJTlBmVe4GZggjDu_nWHsB_o";
    const url = `https://securetoken.googleapis.com/v1/token?key=${apiKey}`;

    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        grant_type: "refresh_token",
        refresh_token: refreshToken,
      }),
    });

    if (!response.ok) {
      console.error("❌ Token refresh failed:", response.status);
      return null;
    }

    const data = await response.json();
    console.log("✅ Token refreshed successfully");

    return {
      idToken: data.id_token,
      refreshToken: data.refresh_token,
      expiresIn: data.expires_in,
    };
  } catch (error) {
    console.error("❌ Error refreshing token:", error);
    return null;
  }
};

// Get valid ID token (refresh if needed)
const getValidIdToken = async (): Promise<string | null> => {
  console.log("🔍 getValidIdToken: Starting token validation...");
  const authData = localStorage.getItem("adminbuddy_auth");
  if (!authData) {
    console.log("❌ getValidIdToken: No auth data in localStorage");
    return null;
  }

  try {
    const { idToken, refreshToken, expiryTime } = JSON.parse(authData);
    console.log("🔍 getValidIdToken: Token expiry:", new Date(expiryTime));
    console.log("🔍 getValidIdToken: Current time:", new Date());

    // Check if current token is still valid (with 5 min buffer)
    const bufferTime = 5 * 60 * 1000; // 5 minutes
    const timeUntilExpiry = expiryTime - Date.now();
    console.log("🔍 getValidIdToken: Time until expiry (ms):", timeUntilExpiry);
    console.log("🔍 getValidIdToken: Buffer time (ms):", bufferTime);

    if (Date.now() < expiryTime - bufferTime) {
      console.log(
        "✅ getValidIdToken: Token still valid, returning existing token"
      );
      return idToken;
    }

    // Token expired or about to expire, refresh it
    console.log(
      "🔄 getValidIdToken: Token expired or about to expire, refreshing..."
    );
    const refreshResult = await refreshAuthToken(refreshToken);

    if (refreshResult) {
      // Update localStorage with new tokens
      const newExpiryTime =
        Date.now() + parseInt(refreshResult.expiresIn) * 1000;
      const updatedAuthData = {
        ...JSON.parse(authData),
        idToken: refreshResult.idToken,
        refreshToken: refreshResult.refreshToken,
        expiryTime: newExpiryTime,
      };
      localStorage.setItem("adminbuddy_auth", JSON.stringify(updatedAuthData));
      console.log("💾 Updated localStorage with refreshed tokens");

      return refreshResult.idToken;
    } else {
      // Refresh failed, clear invalid data
      console.log("❌ Token refresh failed, clearing auth data");
      localStorage.removeItem("adminbuddy_auth");
      return null;
    }
  } catch (error) {
    console.error("❌ Error getting valid token:", error);
    localStorage.removeItem("adminbuddy_auth");
    return null;
  }
};

export const callManualGenerateTasks = async (
  tenantId: string,
  date?: string,
  scheduleId?: string
): Promise<{ success: boolean; tasksCreated: number; message: string }> => {
  try {
    console.log("☁️ Calling manual task generation function via REST API");

    // Debug: Check current auth state
    const authData = localStorage.getItem("adminbuddy_auth");
    if (authData) {
      const { expiryTime } = JSON.parse(authData);
      console.log("🔍 Current token expiry:", new Date(expiryTime));
      console.log("🔍 Current time:", new Date());
      console.log("🔍 Token expired?", Date.now() > expiryTime);
    }

    // Get valid auth token (with automatic refresh if needed)
    const idToken = await getValidIdToken();
    if (!idToken) {
      throw new Error("No valid authentication token available");
    }

    console.log(
      "🔐 Using token for Cloud Function call (length:",
      idToken.length,
      ")"
    );

    // Call Cloud Function via REST API
    const functionUrl =
      "https://us-central1-adminbuddy.cloudfunctions.net/manualGenerateTasks";

    console.log("📡 Sending request to Cloud Function:", {
      tenantId,
      date,
      scheduleId,
      url: functionUrl,
    });

    const requestData: any = { tenantId, date };
    if (scheduleId) {
      requestData.scheduleId = scheduleId;
    }

    const response = await fetch(functionUrl, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${idToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        data: requestData,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("❌ Cloud Function error:", response.status, errorText);
      console.error("❌ Full response:", {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
      });
      throw new Error(
        `Cloud Function failed: ${response.status} - ${response.statusText}`
      );
    }

    const result = await response.json();
    console.log("☁️ Raw Cloud Function response:", result);

    // Handle both direct response and wrapped response formats
    const data = result.result || result.data || result;

    console.log("✅ Manual task generation completed:", data);
    return data;
  } catch (error) {
    console.error("❌ Error calling manual task generation:", error);
    throw error;
  }
};

export const callManualExpireTasks = async (): Promise<{
  success: boolean;
  expiredCount: number;
  message: string;
}> => {
  try {
    console.log("☁️ Calling manual task expiry function via REST API");

    // Get valid auth token (with automatic refresh if needed)
    const idToken = await getValidIdToken();
    if (!idToken) {
      throw new Error("No valid authentication token available");
    }

    // Call Cloud Function via REST API
    const functionUrl =
      "https://us-central1-adminbuddy.cloudfunctions.net/manualExpireTasks";

    const response = await fetch(functionUrl, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${idToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        data: {},
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("❌ Cloud Function error:", response.status, errorText);
      throw new Error(`Cloud Function failed: ${response.status}`);
    }

    const result = await response.json();
    const data = result.result || result;

    console.log("✅ Manual task expiry completed:", data);
    return data;
  } catch (error) {
    console.error("❌ Error calling manual task expiry:", error);
    throw error;
  }
};

export const getFunctionStats = async (): Promise<{
  success: boolean;
  stats: {
    activeTenants: number;
    todaysTasks: {
      total: number;
      pending: number;
      completed: number;
      expired: number;
    };
    adHocTasks: {
      total: number;
      pending: number;
      completed: number;
      expired: number;
    };
    lastUpdated: string;
  };
}> => {
  try {
    console.log("☁️ Calling function stats");
    const getFunctionStatsFunc = httpsCallable(functions, "getFunctionStats");

    const result = await getFunctionStatsFunc({});
    const data = result.data as any;

    console.log("✅ Function stats retrieved:", data);
    return data;
  } catch (error) {
    console.error("❌ Error getting function stats:", error);
    throw error;
  }
};

// Location Reports Management
export const getLastReportTimestamp = async (
  tenantId: string,
  locationId: string
): Promise<string | null> => {
  try {
    console.log("🔍 Getting last report timestamp for location:", locationId);
    const { queryDocuments } = await import("./firestoreRestApi");

    const reports = await queryDocuments(
      ADMIN_BUDDY_COLLECTIONS.LOCATION_REPORTS,
      [
        { field: "tenantId", operator: "==", value: tenantId },
        { field: "locationId", operator: "==", value: locationId },
      ],
      [{ field: "reportTimestamp", direction: "desc" }],
      1 // Only get the most recent
    );

    if (reports.length > 0) {
      const lastTimestamp = reports[0].reportTimestamp;
      console.log("✅ Found last report timestamp:", lastTimestamp);
      return lastTimestamp;
    }

    console.log("📝 No previous reports found for this location");
    return null;
  } catch (error) {
    console.error("❌ Error getting last report timestamp:", error);
    throw error;
  }
};

export const getCompletedTasksSinceLastReport = async (
  tenantId: string,
  locationId: string,
  sinceTimestamp: string | null
): Promise<any[]> => {
  try {
    console.log(
      "🔍 Getting completed tasks since:",
      sinceTimestamp || "beginning of time"
    );
    const { queryDocuments } = await import("./firestoreRestApi");

    // Get all completed daily tasks for this location that haven't been reported yet
    const dailyTaskFilters: any[] = [
      { field: "tenantId", operator: "==", value: tenantId },
      { field: "locationId", operator: "==", value: locationId },
      { field: "status", operator: "==", value: "completed" },
    ];

    // Add timestamp filter if we have a last report time
    if (sinceTimestamp) {
      dailyTaskFilters.push({
        field: "completedAt",
        operator: ">",
        value: sinceTimestamp,
      });
    }

    const completedDailyTasks = await queryDocuments(
      ADMIN_BUDDY_COLLECTIONS.DAILY_TASKS,
      dailyTaskFilters
      // Temporarily removing ordering: [{ field: "completedAt", direction: "desc" }]
    );

    // Filter out tasks that have already been reported
    console.log("🔍 Checking daily tasks for reportedAt field:");
    completedDailyTasks.forEach((task: any, index: number) => {
      console.log(`📋 Daily Task ${index + 1}: ${task.title}`, {
        id: task.id,
        reportedAt: task.reportedAt,
        hasReportedAt: !!task.reportedAt,
        willBeFiltered: !!task.reportedAt,
      });
    });

    const unreportedDailyTasks = completedDailyTasks.filter(
      (task: any) => !task.reportedAt
    );

    console.log(
      `📊 Daily tasks: ${completedDailyTasks.length} total, ${unreportedDailyTasks.length} unreported`
    );

    // Manually sort by completedAt (most recent first) since we can't use ordering
    unreportedDailyTasks.sort(
      (a: any, b: any) =>
        new Date(b.completedAt || 0).getTime() -
        new Date(a.completedAt || 0).getTime()
    );

    // Get all completed ad-hoc tasks for this location
    const adHocTaskFilters: any[] = [
      { field: "tenantId", operator: "==", value: tenantId },
      { field: "locationId", operator: "==", value: locationId },
      { field: "status", operator: "==", value: "completed" },
    ];

    if (sinceTimestamp) {
      adHocTaskFilters.push({
        field: "completedAt",
        operator: ">",
        value: sinceTimestamp,
      });
    }

    const completedAdHocTasks = await queryDocuments(
      ADMIN_BUDDY_COLLECTIONS.AD_HOC_TASKS,
      adHocTaskFilters
      // Temporarily removing ordering: [{ field: "completedAt", direction: "desc" }]
    );

    // Filter out tasks that have already been reported
    const unreportedAdHocTasks = completedAdHocTasks.filter(
      (task: any) => !task.reportedAt
    );

    // Manually sort by completedAt (most recent first) since we can't use ordering
    unreportedAdHocTasks.sort(
      (a: any, b: any) =>
        new Date(b.completedAt || 0).getTime() -
        new Date(a.completedAt || 0).getTime()
    );

    // Combine and format the tasks
    const allCompletedTasks = [
      ...unreportedDailyTasks.map((task: any) => ({
        ...task,
        type: "daily",
      })),
      ...unreportedAdHocTasks.map((task: any) => ({
        ...task,
        type: "ad-hoc",
      })),
    ];

    console.log(
      `✅ Found ${allCompletedTasks.length} completed tasks since last report`
    );
    return allCompletedTasks;
  } catch (error) {
    console.error("❌ Error getting completed tasks since last report:", error);
    throw error;
  }
};

export const getCurrentPendingTasks = async (
  tenantId: string,
  locationId: string,
  dateFilter?: string // Optional date filter (YYYY-MM-DD format)
): Promise<any[]> => {
  try {
    console.log("🔍 Getting current pending tasks for location:", locationId);
    if (dateFilter) {
      console.log("📅 Filtering tasks for date:", dateFilter);
    }
    const { queryDocuments } = await import("./firestoreRestApi");

    // Build filters for daily tasks
    const dailyTaskFilters: Array<{
      field: string;
      operator: "==" | "!=" | "<" | "<=" | ">" | ">=";
      value: any;
    }> = [
      { field: "tenantId", operator: "==", value: tenantId },
      { field: "locationId", operator: "==", value: locationId },
      { field: "status", operator: "==", value: "pending" },
    ];

    // Add date filter if provided (for daily tasks)
    if (dateFilter) {
      dailyTaskFilters.push({
        field: "date",
        operator: "==",
        value: dateFilter,
      });
    }

    // Get pending daily tasks
    const pendingDailyTasks = await queryDocuments(
      ADMIN_BUDDY_COLLECTIONS.DAILY_TASKS,
      dailyTaskFilters
    );

    // Build filters for expired daily tasks
    const expiredDailyTaskFilters: Array<{
      field: string;
      operator: "==" | "!=" | "<" | "<=" | ">" | ">=";
      value: any;
    }> = [
      { field: "tenantId", operator: "==", value: tenantId },
      { field: "locationId", operator: "==", value: locationId },
      { field: "status", operator: "==", value: "expired" },
    ];

    // Add date filter if provided (for daily tasks)
    if (dateFilter) {
      expiredDailyTaskFilters.push({
        field: "date",
        operator: "==",
        value: dateFilter,
      });
    }

    const expiredDailyTasks = await queryDocuments(
      ADMIN_BUDDY_COLLECTIONS.DAILY_TASKS,
      expiredDailyTaskFilters
    );

    // Get pending ad-hoc tasks
    const pendingAdHocTasks = await queryDocuments(
      ADMIN_BUDDY_COLLECTIONS.AD_HOC_TASKS,
      [
        { field: "tenantId", operator: "==", value: tenantId },
        { field: "locationId", operator: "==", value: locationId },
        { field: "status", operator: "==", value: "pending" },
      ]
    );

    const expiredAdHocTasks = await queryDocuments(
      ADMIN_BUDDY_COLLECTIONS.AD_HOC_TASKS,
      [
        { field: "tenantId", operator: "==", value: tenantId },
        { field: "locationId", operator: "==", value: locationId },
        { field: "status", operator: "==", value: "expired" },
      ]
    );

    const allPendingTasks = [
      ...pendingDailyTasks.map((task: any) => ({
        ...task,
        type: "daily",
      })),
      ...expiredDailyTasks.map((task: any) => ({
        ...task,
        type: "daily",
      })),
      ...pendingAdHocTasks.map((task: any) => ({
        ...task,
        type: "ad-hoc",
      })),
      ...expiredAdHocTasks.map((task: any) => ({
        ...task,
        type: "ad-hoc",
      })),
    ];

    // Filter out tasks that have already been reported (same as completed tasks logic)
    const unreportedPendingTasks = allPendingTasks.filter(
      (task: any) => !task.reportedAt
    );

    console.log(
      `✅ Found ${allPendingTasks.length} total pending/expired tasks, ${unreportedPendingTasks.length} unreported`
    );
    return unreportedPendingTasks;
  } catch (error) {
    console.error("❌ Error getting current pending tasks:", error);
    throw error;
  }
};

// Expire pending tasks that have expiryType "next_report" when a report is generated
const expirePendingNextReportTasks = async (
  tasksToExpire: any[],
  reportTimestamp: string
): Promise<void> => {
  try {
    if (tasksToExpire.length === 0) {
      console.log("✅ No pending tasks with 'next_report' expiry to expire");
      return;
    }

    console.log(
      `⏰ Expiring ${tasksToExpire.length} pending tasks with 'next_report' expiry...`
    );
    console.log(
      "📋 Tasks to expire:",
      tasksToExpire.map((t) => ({ id: t.id, title: t.title, type: t.type }))
    );

    const { updateDocument } = await import("./firestoreRestApi");

    // Update each task to mark it as expired
    const updatePromises = tasksToExpire.map(async (task: any) => {
      const collection =
        task.type === "daily"
          ? ADMIN_BUDDY_COLLECTIONS.DAILY_TASKS
          : ADMIN_BUDDY_COLLECTIONS.AD_HOC_TASKS;

      console.log(`⏰ Expiring task ${task.id} in collection ${collection}`);

      // Update task status to expired and mark when it was expired
      // NOTE: Don't set reportedAt yet - that will be done later with completed tasks
      const result = await updateDocument(collection, task.id, {
        status: "expired",
        expiredAt: reportTimestamp,
        tenantId: task.tenantId, // Include tenantId to satisfy security rules
      });

      console.log(
        `✅ Task ${task.id} expired (will be marked as reported later)`
      );
      return result;
    });

    await Promise.all(updatePromises);
    console.log(
      `✅ Successfully expired ${tasksToExpire.length} next_report tasks`
    );
  } catch (error) {
    console.error("❌ Error expiring next_report tasks:", error);
    throw error;
  }
};

// Mark tasks as reported to prevent them from appearing in future reports
const markTasksAsReported = async (
  tasks: any[],
  reportTimestamp: string
): Promise<void> => {
  try {
    console.log(
      `📝 Marking ${tasks.length} tasks as reported at ${reportTimestamp}...`
    );
    console.log(
      "📋 Tasks to mark:",
      tasks.map((t) => ({ id: t.id, title: t.title, type: t.type }))
    );

    const { updateDocument } = await import("./firestoreRestApi");

    // Update each task to mark it as reported
    const updatePromises = tasks.map(async (task: any) => {
      const collection =
        task.type === "daily"
          ? ADMIN_BUDDY_COLLECTIONS.DAILY_TASKS
          : ADMIN_BUDDY_COLLECTIONS.AD_HOC_TASKS;

      console.log(`🔄 Updating task ${task.id} in collection ${collection}`);

      // Include tenantId in the update to satisfy security rules
      const result = await updateDocument(collection, task.id, {
        reportedAt: reportTimestamp,
        tenantId: task.tenantId, // Include tenantId to satisfy security rules
      });

      console.log(`✅ Task ${task.id} marked as reported`);
      return result;
    });

    await Promise.all(updatePromises);
    console.log(`✅ Successfully marked ${tasks.length} tasks as reported`);
  } catch (error) {
    console.error("❌ Error marking tasks as reported:", error);
    console.error("❌ Error details:", error);
    // Don't throw here - we don't want to fail the report creation if this fails
    // The report is already created, this is just cleanup
  }
};

// Utility function to manually mark completed tasks as reported (for debugging/fixing)
export const manuallyMarkCompletedTasksAsReported = async (
  tenantId: string,
  locationId: string
): Promise<void> => {
  try {
    console.log("🔧 Manually marking all completed tasks as reported...");

    // Get all completed tasks for this location
    const completedTasks = await getCompletedTasksSinceLastReport(
      tenantId,
      locationId,
      null // Get all completed tasks, not just since last report
    );

    const reportTimestamp = new Date().toISOString();

    // Mark them all as reported
    await markTasksAsReported(completedTasks, reportTimestamp);

    console.log(
      `✅ Manually marked ${completedTasks.length} completed tasks as reported`
    );
  } catch (error) {
    console.error("❌ Error manually marking tasks as reported:", error);
    throw error;
  }
};

export const createLocationReport = async (
  tenantId: string,
  locationId: string,
  locationName: string,
  reportedBy: string,
  reportNotes: string
): Promise<string> => {
  try {
    console.log("📊 Creating location report for:", locationName);
    const { createDocument } = await import("./firestoreRestApi");

    const reportTimestamp = new Date().toISOString();

    // Get the last report timestamp to determine the period
    const lastReportTimestamp = await getLastReportTimestamp(
      tenantId,
      locationId
    );
    const fromTimestamp = lastReportTimestamp || "1970-01-01T00:00:00.000Z"; // Beginning of time if no previous report

    // Get completed tasks since last report
    const completedTasks = await getCompletedTasksSinceLastReport(
      tenantId,
      locationId,
      lastReportTimestamp
    );

    // Get current pending/expired tasks (no date filter - all unreported tasks)
    const pendingTasks = await getCurrentPendingTasks(tenantId, locationId);

    // FIRST: Expire pending tasks that have expiryType "next_report" BEFORE creating the report
    console.log(
      "⏰ About to expire pending tasks with 'next_report' expiry BEFORE creating report..."
    );
    const nextReportTasksToExpire = pendingTasks.filter(
      (task: any) =>
        task.status === "pending" && task.expiryType === "next_report"
    );

    if (nextReportTasksToExpire.length > 0) {
      await expirePendingNextReportTasks(
        nextReportTasksToExpire,
        reportTimestamp
      );
      console.log(
        "⏰ Finished expiring next_report tasks BEFORE report creation"
      );

      // Update the status of expired tasks in our local pendingTasks array
      // instead of fetching from server (which might filter them out)
      nextReportTasksToExpire.forEach((expiredTask) => {
        const taskInArray = pendingTasks.find((t) => t.id === expiredTask.id);
        if (taskInArray) {
          taskInArray.status = "expired";
          taskInArray.expiredAt = reportTimestamp;
          console.log(
            `📝 Updated local task ${expiredTask.id} status to expired`
          );
        }
      });

      console.log(
        `📋 After expiry: ${pendingTasks.length} current pending/expired tasks (${nextReportTasksToExpire.length} newly expired)`
      );
    } else {
      console.log("✅ No pending tasks with 'next_report' expiry to expire");
    }

    // Format completed tasks for the report
    const completedSinceLastReport = completedTasks.map((task: any) => ({
      id: task.id,
      title: task.title,
      routineName: task.routineName,
      requiredRole: task.requiredRole,
      completedAt: task.completedAt,
      notes: task.notes,
    }));

    // Separate pending and expired tasks for the report
    const currentPendingTasks = pendingTasks
      .filter((task: any) => task.status === "pending")
      .map((task: any) => ({
        id: task.id,
        title: task.title,
        routineName: task.routineName,
        requiredRole: task.requiredRole,
        dueTime: task.dueTime,
        notes: task.notes,
        status: task.status,
      }));

    const expiredTasks = pendingTasks
      .filter((task: any) => task.status === "expired")
      .map((task: any) => ({
        id: task.id,
        title: task.title,
        routineName: task.routineName,
        requiredRole: task.requiredRole,
        dueTime: task.dueTime,
        notes: task.notes,
        status: task.status,
        expiredAt: task.expiredAt,
      }));

    // Calculate summary statistics (after expiring next_report tasks)
    const currentPending = pendingTasks.filter(
      (t: any) => t.status === "pending"
    ).length;
    const currentExpired = pendingTasks.filter(
      (t: any) => t.status === "expired"
    ).length;

    const taskSummary = {
      totalTasksInPeriod: completedTasks.length + pendingTasks.length,
      completedInPeriod: completedTasks.length,
      currentPending: currentPending,
      currentExpired: currentExpired,
      expiredByThisReport: nextReportTasksToExpire.length,
    };

    // Create the report object
    const report = {
      tenantId,
      locationId,
      locationName,
      reportTimestamp,
      reportedBy,
      reportPeriod: {
        fromTimestamp,
        toTimestamp: reportTimestamp,
      },
      taskSummary,
      completedSinceLastReport,
      currentPendingTasks,
      expiredTasks,
      reportNotes,
      createdAt: reportTimestamp,
    };

    // Save the report to Firestore
    const reportId = await createDocument(
      ADMIN_BUDDY_COLLECTIONS.LOCATION_REPORTS,
      report
    );

    // Mark all completed and expired tasks as reported
    const allTasksToReport = [...completedTasks, ...expiredTasks];
    console.log(
      `🔄 About to mark ${allTasksToReport.length} tasks as reported (${completedTasks.length} completed, ${expiredTasks.length} expired)...`
    );
    await markTasksAsReported(allTasksToReport, reportTimestamp);
    console.log("🔄 Finished marking tasks as reported");

    console.log("✅ Location report created successfully:", reportId);
    console.log("📊 Report summary:", {
      period: `${fromTimestamp} to ${reportTimestamp}`,
      completed: completedTasks.length,
      pending: pendingTasks.filter((t: any) => t.status === "pending").length,
      expired: pendingTasks.filter((t: any) => t.status === "expired").length,
      expiredByReport: nextReportTasksToExpire.length,
    });

    return reportId;
  } catch (error) {
    console.error("❌ Error creating location report:", error);
    throw error;
  }
};

// User Profile Management
export interface UserProfile {
  uid: string;
  email: string;
  tenantId: string;
  role: "owner" | "manager" | "employee";
  displayName?: string;
  createdAt: string;
  lastLoginAt: string;
  // Trial Management
  trialStartDate?: string; // ISO date when trial started
  trialEndDate?: string; // ISO date when trial ends (14 days from start)
  hasActiveSubscription?: boolean; // Quick check for subscription status
}

export const getUserProfileByEmail = async (
  email: string
): Promise<UserProfile | null> => {
  try {
    const q = query(
      collection(db, "user_profiles"),
      where("email", "==", email),
      limit(1)
    );
    const querySnapshot = await getDocs(q);

    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      return { uid: doc.id, ...doc.data() } as UserProfile;
    }

    return null;
  } catch (error) {
    console.error("Error getting user profile by email:", error);
    throw error;
  }
};

export const createNewUserWithTenant = async (
  email: string
): Promise<UserProfile> => {
  try {
    // Generate unique IDs
    const uid = `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const tenantId = `tenant-${Date.now()}-${Math.random()
      .toString(36)
      .substr(2, 9)}`;

    // Create user profile
    const userProfile: UserProfile = {
      uid,
      email,
      tenantId,
      role: "owner", // First user becomes owner
      displayName: email.split("@")[0],
      createdAt: new Date().toISOString(),
      lastLoginAt: new Date().toISOString(),
    };

    // Save user profile to Firestore
    await setDoc(doc(db, "user_profiles", uid), userProfile);

    // Create default tenant roles for the new tenant
    await createDefaultTenantRoles(tenantId);

    console.log("✅ Created new user and tenant:", { email, tenantId });
    return userProfile;
  } catch (error) {
    console.error("Error creating new user with tenant:", error);
    throw error;
  }
};

// Simple password hashing (for demo - in production use proper bcrypt)
const hashPassword = async (password: string): Promise<string> => {
  const encoder = new TextEncoder();
  const data = encoder.encode(password + "adminbuddy_salt_2024");
  const hashBuffer = await crypto.subtle.digest("SHA-256", data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map((b) => b.toString(16).padStart(2, "0")).join("");
};

export const createNewUserWithPassword = async (
  email: string,
  password: string
): Promise<UserProfile> => {
  try {
    // Generate unique IDs
    const uid = `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const tenantId = `tenant-${Date.now()}-${Math.random()
      .toString(36)
      .substr(2, 9)}`;

    // Hash password
    const passwordHash = await hashPassword(password);

    // Create user profile with password
    const userProfile: UserProfile = {
      uid,
      email,
      tenantId,
      role: "owner", // First user becomes owner
      displayName: email.split("@")[0],
      createdAt: new Date().toISOString(),
      lastLoginAt: new Date().toISOString(),
    };

    // Save user profile to Firestore
    await setDoc(doc(db, "user_profiles", uid), userProfile);

    // Save password hash separately for security
    await setDoc(doc(db, "user_auth", uid), {
      email,
      passwordHash,
      createdAt: new Date().toISOString(),
    });

    // Create default tenant roles for the new tenant
    await createDefaultTenantRoles(tenantId);

    console.log("✅ Created new user with password and tenant:", {
      email,
      tenantId,
    });
    return userProfile;
  } catch (error) {
    console.error("Error creating new user with password:", error);
    throw error;
  }
};

export const verifyUserCredentials = async (
  email: string,
  password: string
): Promise<UserProfile | null> => {
  try {
    // Get user profile by email
    const userProfile = await getUserProfileByEmail(email);
    if (!userProfile) {
      return null;
    }

    // Get password hash
    const authDoc = await getDoc(doc(db, "user_auth", userProfile.uid));
    if (!authDoc.exists()) {
      return null;
    }

    const authData = authDoc.data();
    const storedHash = authData.passwordHash;

    // Verify password
    const inputHash = await hashPassword(password);
    if (inputHash !== storedHash) {
      return null;
    }

    // Update last login time
    const updatedProfile = {
      ...userProfile,
      lastLoginAt: new Date().toISOString(),
    };
    await setDoc(doc(db, "user_profiles", userProfile.uid), updatedProfile);

    console.log("✅ User credentials verified:", email);
    return updatedProfile;
  } catch (error) {
    console.error("Error verifying user credentials:", error);
    return null;
  }
};

// Firebase Auth compatible user profile functions
export const getUserProfile = async (
  uid: string,
  idToken?: string
): Promise<UserProfile | null> => {
  try {
    console.log("🔍 getUserProfile: Starting lookup for UID:", uid);
    console.log("🚀 Using Firestore REST API for production compatibility...");

    // Get auth token - either passed in or from localStorage
    let token = idToken;
    if (!token) {
      const authData = localStorage.getItem("adminbuddy_auth");
      if (authData) {
        const { idToken: storedToken } = JSON.parse(authData);
        token = storedToken;
        console.log("🔐 Using stored auth token from localStorage");
      } else {
        console.log("❌ No auth token available");
        return null;
      }
    } else {
      console.log("🔐 Using provided auth token");
    }

    // Use Firestore REST API directly (bypasses SDK issues)
    const projectId = "adminbuddy";
    const url = `https://firestore.googleapis.com/v1/projects/${projectId}/databases/(default)/documents/user_profiles/${uid}`;

    console.log("📡 Calling Firestore REST API with auth...");
    const response = await fetch(url, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });
    console.log("📡 Firestore API response status:", response.status);

    if (response.status === 404) {
      console.log("❌ getUserProfile: Document does not exist");
      return null;
    }

    if (!response.ok) {
      console.error(
        "❌ Firestore API error:",
        response.status,
        response.statusText
      );
      return null;
    }

    const docData = await response.json();
    console.log("✅ Firestore API success, extracting data...");
    console.log("📡 Raw Firestore response:", JSON.stringify(docData, null, 2));

    // Convert Firestore REST API format to our UserProfile format
    const fields = docData.fields || {};
    console.log("📋 Extracted fields:", Object.keys(fields));
    const profile: UserProfile = {
      uid,
      email: fields.email?.stringValue || "",
      tenantId: fields.tenantId?.stringValue || "",
      role:
        (fields.role?.stringValue as "owner" | "manager" | "employee") ||
        "owner",
      displayName: fields.displayName?.stringValue || "",
      createdAt: fields.createdAt?.stringValue || "",
      lastLoginAt: fields.lastLoginAt?.stringValue || "",
      // Trial Management
      trialStartDate: fields.trialStartDate?.stringValue || undefined,
      trialEndDate: fields.trialEndDate?.stringValue || undefined,
      hasActiveSubscription:
        fields.hasActiveSubscription?.booleanValue || false,
    };

    console.log("👤 getUserProfile: Profile extracted:", {
      email: profile.email,
      tenantId: profile.tenantId,
      role: profile.role,
      uid: profile.uid,
    });

    // Validate that we got the essential fields
    if (!profile.email || !profile.tenantId) {
      console.error("❌ getUserProfile: Missing essential fields", {
        email: profile.email,
        tenantId: profile.tenantId,
        rawFields: fields,
      });
      return null;
    }

    return profile;
  } catch (error) {
    console.error("❌ getUserProfile: Error occurred:", error);
    return null;
  }
};

export const createUserProfile = async (user: any): Promise<UserProfile> => {
  try {
    console.log("👤 Creating user profile via REST API for:", user.email);

    // Generate a unique tenant ID for the new user
    const tenantId = `tenant-${Date.now()}-${Math.random()
      .toString(36)
      .substr(2, 9)}`;

    // Initialize trial dates
    const now = new Date().toISOString();
    const trialEnd = new Date();
    trialEnd.setDate(trialEnd.getDate() + 14);
    const trialEndDate = trialEnd.toISOString();

    const userProfile: UserProfile = {
      uid: user.uid,
      email: user.email,
      tenantId,
      role: "owner", // First user becomes owner
      displayName: user.displayName || user.email.split("@")[0],
      createdAt: now,
      lastLoginAt: now,
      // Trial Management
      trialStartDate: now,
      trialEndDate: trialEndDate,
      hasActiveSubscription: false,
    };

    // Get auth token from localStorage
    const authData = localStorage.getItem("adminbuddy_auth");
    if (!authData) {
      throw new Error("No authentication token available for profile creation");
    }

    const { idToken } = JSON.parse(authData);
    console.log("🔐 Using auth token for profile creation");

    // Use Firestore REST API to create user profile
    const projectId = "adminbuddy";
    const url = `https://firestore.googleapis.com/v1/projects/${projectId}/databases/(default)/documents/user_profiles/${user.uid}`;

    // Convert profile data to Firestore REST API format
    const firestoreData = {
      fields: {
        uid: { stringValue: userProfile.uid },
        email: { stringValue: userProfile.email },
        tenantId: { stringValue: userProfile.tenantId },
        role: { stringValue: userProfile.role },
        displayName: { stringValue: userProfile.displayName },
        createdAt: { stringValue: userProfile.createdAt },
        lastLoginAt: { stringValue: userProfile.lastLoginAt },
        // Trial Management
        trialStartDate: { stringValue: userProfile.trialStartDate || "" },
        trialEndDate: { stringValue: userProfile.trialEndDate || "" },
        hasActiveSubscription: {
          booleanValue: userProfile.hasActiveSubscription || false,
        },
      },
    };

    console.log("📡 Creating user profile via Firestore REST API...");
    const response = await fetch(url, {
      method: "PATCH",
      headers: {
        Authorization: `Bearer ${idToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(firestoreData),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error(
        "❌ Failed to create user profile:",
        response.status,
        errorData
      );
      throw new Error(
        `Failed to create user profile: ${response.status} ${errorData}`
      );
    }

    console.log("✅ User profile created successfully via REST API");

    // Create default tenant roles for the new tenant
    await createDefaultTenantRoles(tenantId);

    console.log("✅ Created user profile and tenant:", {
      email: user.email,
      tenantId,
    });
    return userProfile;
  } catch (error) {
    console.error("❌ Error creating user profile:", error);
    throw error;
  }
};
