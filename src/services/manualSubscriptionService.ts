// Manual Subscription Update Service for Testing
import { httpsCallable } from "firebase/functions";
import { functions } from "../config/firebase";

interface ManualSubscriptionData {
  tenantId: string;
  subscriptionId?: string;
  locationCount?: number;
  status?: "active" | "canceled" | "past_due" | "unpaid" | "incomplete";
}

/**
 * Manually update subscription status for testing purposes
 * This bypasses Stripe webhooks and directly updates the database
 */
export const manualUpdateSubscription = async (data: ManualSubscriptionData) => {
  try {
    console.log("🔧 Manually updating subscription:", data);

    const manualUpdateFunction = httpsCallable(functions, "manualUpdateSubscription");
    const result = await manualUpdateFunction(data);

    console.log("✅ Manual subscription update successful:", result.data);
    return result.data;
  } catch (error) {
    console.error("❌ Manual subscription update failed:", error);
    throw error;
  }
};

/**
 * Quick function to activate subscription for current user
 */
export const activateSubscriptionForTenant = async (
  tenantId: string, 
  locationCount: number = 3
) => {
  return manualUpdateSubscription({
    tenantId,
    locationCount,
    status: "active",
    subscriptionId: `manual_${Date.now()}`,
  });
};

/**
 * Quick function to cancel subscription for current user
 */
export const cancelSubscriptionForTenant = async (tenantId: string) => {
  return manualUpdateSubscription({
    tenantId,
    locationCount: 1,
    status: "canceled",
  });
};
