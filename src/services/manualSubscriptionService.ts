// Manual Subscription Update Service for Testing
import {
  doc,
  updateDoc,
  setDoc,
  getDoc,
  collection,
  query,
  where,
  getDocs,
  writeBatch,
} from "firebase/firestore";
import { db } from "../config/firebase";

interface ManualSubscriptionData {
  tenantId: string;
  subscriptionId?: string;
  locationCount?: number;
  status?: "active" | "canceled" | "past_due" | "unpaid" | "incomplete";
}

/**
 * Manually update subscription status for testing purposes
 * This bypasses Stripe webhooks and directly updates the database
 */
export const manualUpdateSubscription = async (
  data: ManualSubscriptionData
) => {
  try {
    console.log("🔧 Manually updating subscription:", data);

    const {
      tenantId,
      locationCount = 3,
      status = "active",
      subscriptionId = `manual_${Date.now()}`,
    } = data;

    // Update tenant document
    await updateTenantSubscription(tenantId, {
      subscriptionId,
      status,
      locationCount,
    });

    // Update user profiles for this tenant
    await updateUserSubscriptionStatus(tenantId, status === "active");

    console.log("✅ Manual subscription update successful");
    return {
      success: true,
      tenantId,
      status,
      locationCount,
    };
  } catch (error) {
    console.error("❌ Manual subscription update failed:", error);
    throw error;
  }
};

/**
 * Update tenant subscription data directly in Firestore
 */
const updateTenantSubscription = async (
  tenantId: string,
  data: { subscriptionId: string; status: string; locationCount: number }
) => {
  try {
    const tenantRef = doc(db, "tenants", tenantId);
    const tenantDoc = await getDoc(tenantRef);

    const updateData = {
      subscriptionId: data.subscriptionId,
      subscriptionStatus: data.status,
      subscriptionCurrentPeriodEnd: new Date(
        Date.now() + 30 * 24 * 60 * 60 * 1000
      ).toISOString(), // 30 days from now
      subscriptionLocationCount: data.locationCount,
      lastPaymentDate:
        data.status === "active" ? new Date().toISOString() : undefined,
      updatedAt: new Date().toISOString(),
    };

    // Remove undefined values
    Object.keys(updateData).forEach((key) => {
      if (updateData[key as keyof typeof updateData] === undefined) {
        delete updateData[key as keyof typeof updateData];
      }
    });

    if (!tenantDoc.exists()) {
      // Create tenant document if it doesn't exist
      const newTenantData = {
        id: tenantId,
        name: "New Business",
        plan: "basic",
        isActive: true,
        createdAt: new Date().toISOString(),
        settings: {
          timezone: "America/Toronto",
          dateFormat: "MM/DD/YYYY",
          timeFormat: "12h" as const,
        },
        ...updateData,
      };

      await setDoc(tenantRef, newTenantData);
      console.log("✅ Tenant created with subscription data");
    } else {
      // Update existing tenant
      await updateDoc(tenantRef, updateData);
      console.log("✅ Tenant subscription updated");
    }
  } catch (error) {
    console.error("❌ Error updating tenant subscription:", error);
    throw error;
  }
};

/**
 * Update user profiles subscription status
 */
const updateUserSubscriptionStatus = async (
  tenantId: string,
  hasActiveSubscription: boolean
) => {
  try {
    // Find all user profiles for this tenant
    const userProfilesRef = collection(db, "user_profiles");
    const q = query(userProfilesRef, where("tenantId", "==", tenantId));
    const snapshot = await getDocs(q);

    if (snapshot.empty) {
      console.warn("No user profiles found for tenant:", tenantId);
      return;
    }

    // Update all user profiles in a batch
    const batch = writeBatch(db);
    snapshot.docs.forEach((docSnapshot) => {
      batch.update(docSnapshot.ref, {
        hasActiveSubscription,
        lastLoginAt: new Date().toISOString(),
      });
    });

    await batch.commit();
    console.log(`✅ Updated ${snapshot.docs.length} user profiles`);
  } catch (error) {
    console.error("❌ Error updating user subscription status:", error);
    throw error;
  }
};

/**
 * Quick function to activate subscription for current user
 */
export const activateSubscriptionForTenant = async (
  tenantId: string,
  locationCount: number = 3
) => {
  return manualUpdateSubscription({
    tenantId,
    locationCount,
    status: "active",
    subscriptionId: `manual_${Date.now()}`,
  });
};

/**
 * Quick function to cancel subscription for current user
 */
export const cancelSubscriptionForTenant = async (tenantId: string) => {
  return manualUpdateSubscription({
    tenantId,
    locationCount: 1,
    status: "canceled",
  });
};
