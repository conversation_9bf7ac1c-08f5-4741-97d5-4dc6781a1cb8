// AdminBuddy User Management Service
import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
} from "firebase/firestore";
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  User as FirebaseUser,
} from "firebase/auth";
import { auth, db, COLLECTIONS } from "../config/firebase";
import { User, UserRole } from "../types";

// Current user session management
let currentUser: User | null = null;

// Create a new user account
export const createUser = async (
  userData: Omit<User, "id" | "createdAt" | "updatedAt" | "lastLoginAt">,
  password: string
): Promise<string> => {
  try {
    console.log("👤 Creating new user:", userData.email);

    // Create Firebase Auth account
    const userCredential = await createUserWithEmailAndPassword(
      auth,
      userData.email,
      password
    );

    const now = new Date().toISOString();

    // Create user document in Firestore
    const userDoc = {
      ...userData,
      createdAt: now,
      updatedAt: now,
    };

    // Use the Firebase Auth UID as the document ID
    await doc(db, COLLECTIONS.USERS, userCredential.user.uid);

    console.log(`✅ User created with ID: ${userCredential.user.uid}`);
    return userCredential.user.uid;
  } catch (error) {
    console.error("❌ Error creating user:", error);
    throw error;
  }
};

// Sign in user - RESTORED to use Firebase SDK (React Strict Mode was the issue)
export const signInUser = async (
  email: string,
  password: string
): Promise<User | null> => {
  try {
    console.log("🔐 Signing in user:", email);
    console.log("✅ Using Firebase Auth SDK (React Strict Mode disabled)");

    const userCredential = await signInWithEmailAndPassword(
      auth,
      email,
      password
    );
    console.log("✅ Firebase Auth successful:", userCredential.user.email);

    const user = await getUserById(userCredential.user.uid);

    if (user) {
      console.log("👤 User found in Firestore:", user.email);
      console.log("🏢 User tenant ID:", user.tenantId);

      // Update last login time
      await updateUser(user.id, { lastLoginAt: new Date().toISOString() });
      currentUser = { ...user, lastLoginAt: new Date().toISOString() };

      console.log("🎉 Login completed successfully!");
    }

    return user;
  } catch (error) {
    console.error("❌ Error signing in:", error);
    throw error;
  }
};

// Sign out user
export const signOutUser = async (): Promise<void> => {
  try {
    await signOut(auth);
    currentUser = null;
    console.log("👋 User signed out");
  } catch (error) {
    console.error("❌ Error signing out:", error);
    throw error;
  }
};

// Get current user session
export const getCurrentUser = (): User | null => {
  return currentUser;
};

// Listen for auth state changes
export const onAuthStateChange = (callback: (user: User | null) => void) => {
  return onAuthStateChanged(auth, async (firebaseUser: FirebaseUser | null) => {
    if (firebaseUser) {
      const user = await getUserById(firebaseUser.uid);
      currentUser = user;
      callback(user);
    } else {
      currentUser = null;
      callback(null);
    }
  });
};

// Get user by ID - RESTORED to use Firestore SDK (React Strict Mode was the issue)
export const getUserById = async (userId: string): Promise<User | null> => {
  try {
    console.log("🔍 Getting user by ID:", userId);

    const userRef = doc(db, COLLECTIONS.USERS, userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      console.log("❌ User not found in Firestore");
      return null;
    }

    const userData = { id: userDoc.id, ...userDoc.data() } as User;
    console.log("✅ User found:", userData.email);

    return userData;
  } catch (error) {
    console.error("❌ Error getting user:", error);
    return null;
  }
};

// Get users by tenant
export const getUsersByTenant = async (tenantId: string): Promise<User[]> => {
  try {
    console.log("🔍 Fetching users for tenant:", tenantId);
    const usersRef = collection(db, COLLECTIONS.USERS);
    const q = query(
      usersRef,
      where("tenantId", "==", tenantId),
      orderBy("createdAt", "desc")
    );
    const snapshot = await getDocs(q);

    const users = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as User[];

    console.log(`📊 Found ${users.length} users for tenant`);
    return users;
  } catch (error) {
    console.error("❌ Error fetching users:", error);
    return [];
  }
};

// Update user
export const updateUser = async (
  userId: string,
  updates: Partial<Omit<User, "id" | "createdAt" | "updatedAt">>
): Promise<boolean> => {
  try {
    console.log(`📝 Updating user: ${userId}`);

    const userRef = doc(db, COLLECTIONS.USERS, userId);
    await updateDoc(userRef, {
      ...updates,
      updatedAt: new Date().toISOString(),
    });

    console.log(`✅ User updated successfully`);
    return true;
  } catch (error) {
    console.error("❌ Error updating user:", error);
    return false;
  }
};

// Get users by role
export const getUsersByRole = async (
  tenantId: string,
  role: UserRole
): Promise<User[]> => {
  try {
    const usersRef = collection(db, COLLECTIONS.USERS);
    const q = query(
      usersRef,
      where("tenantId", "==", tenantId),
      where("role", "==", role),
      where("isActive", "==", true)
    );
    const snapshot = await getDocs(q);

    return snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as User[];
  } catch (error) {
    console.error("❌ Error fetching users by role:", error);
    return [];
  }
};

// Check if user has access to location
export const userHasLocationAccess = (
  user: User,
  locationId: string
): boolean => {
  if (user.role === "owner") return true;
  if (user.role === "kiosk") return user.kioskLocationId === locationId;
  if (user.role === "manager")
    return user.locationIds?.includes(locationId) || false;
  return false;
};
