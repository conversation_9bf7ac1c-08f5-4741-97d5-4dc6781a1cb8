// AdminBuddy Subscription Management Service
import { doc, updateDoc, getDoc } from "firebase/firestore";
import { db } from "../config/firebase";
import { Tenant } from "../types";

export interface SubscriptionData {
  subscriptionId: string;
  status: "active" | "canceled" | "past_due" | "unpaid" | "incomplete";
  currentPeriodEnd: string; // ISO date
  locationCount: number;
  stripeCustomerId?: string;
}

/**
 * Update tenant subscription information
 */
export const updateTenantSubscription = async (
  tenantId: string,
  subscriptionData: SubscriptionData
): Promise<void> => {
  try {
    console.log("💳 Updating tenant subscription:", tenantId, subscriptionData);

    const tenantRef = doc(db, "tenants", tenantId);
    
    const updateData = {
      subscriptionId: subscriptionData.subscriptionId,
      subscriptionStatus: subscriptionData.status,
      subscriptionCurrentPeriodEnd: subscriptionData.currentPeriodEnd,
      subscriptionLocationCount: subscriptionData.locationCount,
      lastPaymentDate: subscriptionData.status === "active" ? new Date().toISOString() : undefined,
      updatedAt: new Date().toISOString(),
    };

    // Remove undefined values
    Object.keys(updateData).forEach(key => {
      if (updateData[key as keyof typeof updateData] === undefined) {
        delete updateData[key as keyof typeof updateData];
      }
    });

    await updateDoc(tenantRef, updateData);
    console.log("✅ Tenant subscription updated successfully");
  } catch (error) {
    console.error("❌ Error updating tenant subscription:", error);
    throw error;
  }
};

/**
 * Update user profile subscription status
 */
export const updateUserSubscriptionStatus = async (
  uid: string,
  hasActiveSubscription: boolean
): Promise<void> => {
  try {
    console.log("👤 Updating user subscription status:", uid, hasActiveSubscription);

    const userRef = doc(db, "user_profiles", uid);
    
    await updateDoc(userRef, {
      hasActiveSubscription,
      lastLoginAt: new Date().toISOString(),
    });

    console.log("✅ User subscription status updated successfully");
  } catch (error) {
    console.error("❌ Error updating user subscription status:", error);
    throw error;
  }
};

/**
 * Get tenant subscription information
 */
export const getTenantSubscription = async (tenantId: string): Promise<Partial<Tenant> | null> => {
  try {
    console.log("🔍 Getting tenant subscription:", tenantId);

    const tenantRef = doc(db, "tenants", tenantId);
    const tenantDoc = await getDoc(tenantRef);

    if (!tenantDoc.exists()) {
      console.log("❌ Tenant not found:", tenantId);
      return null;
    }

    const tenantData = tenantDoc.data() as Tenant;
    console.log("✅ Tenant subscription retrieved");

    return {
      subscriptionId: tenantData.subscriptionId,
      subscriptionStatus: tenantData.subscriptionStatus,
      subscriptionCurrentPeriodEnd: tenantData.subscriptionCurrentPeriodEnd,
      subscriptionLocationCount: tenantData.subscriptionLocationCount,
      lastPaymentDate: tenantData.lastPaymentDate,
      stripeCustomerId: tenantData.stripeCustomerId,
    };
  } catch (error) {
    console.error("❌ Error getting tenant subscription:", error);
    return null;
  }
};

/**
 * Check if subscription allows creating more locations
 */
export const canCreateLocation = async (tenantId: string, currentLocationCount: number): Promise<boolean> => {
  try {
    const subscription = await getTenantSubscription(tenantId);
    
    if (!subscription || !subscription.subscriptionStatus) {
      // No subscription - allow during trial (will be enforced elsewhere)
      return true;
    }

    if (subscription.subscriptionStatus !== "active") {
      // Inactive subscription
      return false;
    }

    const allowedLocations = subscription.subscriptionLocationCount || 1;
    return currentLocationCount < allowedLocations;
  } catch (error) {
    console.error("❌ Error checking location creation permission:", error);
    return false;
  }
};

/**
 * Get subscription status summary for UI
 */
export const getSubscriptionSummary = async (tenantId: string) => {
  try {
    const subscription = await getTenantSubscription(tenantId);
    
    if (!subscription || !subscription.subscriptionStatus) {
      return {
        hasSubscription: false,
        status: null,
        locationCount: 0,
        currentPeriodEnd: null,
      };
    }

    return {
      hasSubscription: true,
      status: subscription.subscriptionStatus,
      locationCount: subscription.subscriptionLocationCount || 0,
      currentPeriodEnd: subscription.subscriptionCurrentPeriodEnd,
      isActive: subscription.subscriptionStatus === "active",
    };
  } catch (error) {
    console.error("❌ Error getting subscription summary:", error);
    return {
      hasSubscription: false,
      status: null,
      locationCount: 0,
      currentPeriodEnd: null,
    };
  }
};
