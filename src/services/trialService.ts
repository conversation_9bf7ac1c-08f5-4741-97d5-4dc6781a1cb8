// AdminBuddy Trial Management Service
import { UserProfile } from "./adminBuddyFirebaseService";

export interface TrialStatus {
  isInTrial: boolean;
  isExpired: boolean;
  daysRemaining: number;
  trialEndDate: string | null;
  hasActiveSubscription: boolean;
}

/**
 * Calculate trial end date (14 days from start date)
 */
export const calculateTrialEndDate = (startDate: string): string => {
  const start = new Date(startDate);
  const end = new Date(start);
  end.setDate(start.getDate() + 14);
  return end.toISOString();
};

/**
 * Get trial status for a user profile
 */
export const getTrialStatus = (userProfile: UserProfile | null): TrialStatus => {
  if (!userProfile) {
    return {
      isInTrial: false,
      isExpired: true,
      daysRemaining: 0,
      trialEndDate: null,
      hasActiveSubscription: false,
    };
  }

  const now = new Date();
  const hasActiveSubscription = userProfile.hasActiveSubscription || false;

  // If user has active subscription, trial doesn't matter
  if (hasActiveSubscription) {
    return {
      isInTrial: false,
      isExpired: false,
      daysRemaining: 0,
      trialEndDate: userProfile.trialEndDate || null,
      hasActiveSubscription: true,
    };
  }

  // Check if user has trial dates
  if (!userProfile.trialStartDate || !userProfile.trialEndDate) {
    // No trial info - consider expired
    return {
      isInTrial: false,
      isExpired: true,
      daysRemaining: 0,
      trialEndDate: null,
      hasActiveSubscription: false,
    };
  }

  const trialEnd = new Date(userProfile.trialEndDate);
  const isExpired = now > trialEnd;
  const daysRemaining = Math.max(0, Math.ceil((trialEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)));

  return {
    isInTrial: !isExpired,
    isExpired,
    daysRemaining,
    trialEndDate: userProfile.trialEndDate,
    hasActiveSubscription: false,
  };
};

/**
 * Initialize trial for a new user
 */
export const initializeTrial = (): { trialStartDate: string; trialEndDate: string } => {
  const now = new Date().toISOString();
  const trialEndDate = calculateTrialEndDate(now);
  
  return {
    trialStartDate: now,
    trialEndDate,
  };
};

/**
 * Check if user can access features (has active subscription or trial not expired)
 */
export const canAccessFeatures = (userProfile: UserProfile | null): boolean => {
  const status = getTrialStatus(userProfile);
  return status.hasActiveSubscription || status.isInTrial;
};

/**
 * Get user-friendly trial message
 */
export const getTrialMessage = (userProfile: UserProfile | null): string => {
  const status = getTrialStatus(userProfile);

  if (status.hasActiveSubscription) {
    return "You have an active subscription";
  }

  if (status.isExpired) {
    return "Your free trial has expired. Please subscribe to continue using AdminBuddy.";
  }

  if (status.daysRemaining === 0) {
    return "Your free trial expires today. Subscribe now to continue using AdminBuddy.";
  }

  if (status.daysRemaining === 1) {
    return "Your free trial expires tomorrow. Subscribe now to avoid interruption.";
  }

  return `Your free trial expires in ${status.daysRemaining} days.`;
};

/**
 * Get trial warning level for UI styling
 */
export const getTrialWarningLevel = (userProfile: UserProfile | null): "none" | "info" | "warning" | "error" => {
  const status = getTrialStatus(userProfile);

  if (status.hasActiveSubscription) {
    return "none";
  }

  if (status.isExpired) {
    return "error";
  }

  if (status.daysRemaining <= 2) {
    return "error";
  }

  if (status.daysRemaining <= 5) {
    return "warning";
  }

  return "info";
};
