// Firestore REST API helper service
// Provides CRUD operations using Firestore REST API to avoid Firebase SDK hanging issues

const PROJECT_ID = "adminbuddy";
const BASE_URL = `https://firestore.googleapis.com/v1/projects/${PROJECT_ID}/databases/(default)/documents`;

// Get auth token from localStorage or TenantContext
const getAuthToken = (): string | null => {
  const authData = localStorage.getItem("adminbuddy_auth");
  if (authData) {
    const { idToken } = JSON.parse(authData);
    return idToken;
  }
  return null;
};

// Convert JavaScript object to Firestore REST API format
const toFirestoreValue = (value: any): any => {
  if (value === null || value === undefined) {
    return { nullValue: null };
  }

  if (typeof value === "string") {
    return { stringValue: value };
  }

  if (typeof value === "number") {
    if (Number.isInteger(value)) {
      return { integerValue: value.toString() };
    } else {
      return { doubleValue: value };
    }
  }

  if (typeof value === "boolean") {
    return { booleanValue: value };
  }

  if (Array.isArray(value)) {
    return {
      arrayValue: {
        values: value.map(toFirestoreValue),
      },
    };
  }

  if (typeof value === "object") {
    const fields: any = {};
    for (const [key, val] of Object.entries(value)) {
      fields[key] = toFirestoreValue(val);
    }
    return { mapValue: { fields } };
  }

  return { stringValue: String(value) };
};

// Convert Firestore REST API format to JavaScript object
const fromFirestoreValue = (value: any): any => {
  if (value.nullValue !== undefined) {
    return null;
  }

  if (value.stringValue !== undefined) {
    return value.stringValue;
  }

  if (value.integerValue !== undefined) {
    return parseInt(value.integerValue);
  }

  if (value.doubleValue !== undefined) {
    return value.doubleValue;
  }

  if (value.booleanValue !== undefined) {
    return value.booleanValue;
  }

  if (value.arrayValue !== undefined) {
    return value.arrayValue.values?.map(fromFirestoreValue) || [];
  }

  if (value.mapValue !== undefined) {
    const result: any = {};
    for (const [key, val] of Object.entries(value.mapValue.fields || {})) {
      result[key] = fromFirestoreValue(val);
    }
    return result;
  }

  return value;
};

// Convert Firestore document to JavaScript object
const fromFirestoreDocument = (doc: any): any => {
  const result: any = {};

  if (doc.fields) {
    for (const [key, value] of Object.entries(doc.fields)) {
      result[key] = fromFirestoreValue(value);
    }
  }

  // Extract document ID from name
  if (doc.name) {
    const parts = doc.name.split("/");
    result.id = parts[parts.length - 1];
  }

  return result;
};

// CREATE: Add a new document to a collection
export const createDocument = async (
  collection: string,
  data: any,
  documentId?: string
): Promise<string> => {
  const token = getAuthToken();
  if (!token) {
    throw new Error("No authentication token available");
  }

  console.log(`📡 REST API: Creating document in ${collection}`);

  // Convert data to Firestore format
  const firestoreData = {
    fields: {},
  };

  for (const [key, value] of Object.entries(data)) {
    (firestoreData.fields as any)[key] = toFirestoreValue(value);
  }

  let url = `${BASE_URL}/${collection}`;
  if (documentId) {
    url += `?documentId=${documentId}`;
  }

  const response = await fetch(url, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(firestoreData),
  });

  if (!response.ok) {
    const errorData = await response.text();
    console.error("❌ REST API create error:", response.status, errorData);
    throw new Error(`Failed to create document: ${response.status}`);
  }

  const result = await response.json();
  const parts = result.name.split("/");
  const createdId = parts[parts.length - 1];

  console.log(`✅ REST API: Document created with ID: ${createdId}`);
  return createdId;
};

// READ: Get a single document by ID
export const getDocument = async (
  collection: string,
  documentId: string
): Promise<any | null> => {
  const token = getAuthToken();
  if (!token) {
    throw new Error("No authentication token available");
  }

  console.log(`📡 REST API: Getting document ${documentId} from ${collection}`);

  const url = `${BASE_URL}/${collection}/${documentId}`;

  const response = await fetch(url, {
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
  });

  if (response.status === 404) {
    console.log(`❌ REST API: Document ${documentId} not found`);
    return null;
  }

  if (!response.ok) {
    const errorData = await response.text();
    console.error("❌ REST API get error:", response.status, errorData);
    throw new Error(`Failed to get document: ${response.status}`);
  }

  const result = await response.json();
  const document = fromFirestoreDocument(result);

  console.log(`✅ REST API: Document retrieved: ${documentId}`);
  return document;
};

// UPDATE: Update an existing document
export const updateDocument = async (
  collection: string,
  documentId: string,
  updates: any
): Promise<void> => {
  const token = getAuthToken();
  if (!token) {
    throw new Error("No authentication token available");
  }

  console.log(`📡 REST API: Updating document ${documentId} in ${collection}`);

  // Convert updates to Firestore format
  const firestoreData = {
    fields: {},
  };

  for (const [key, value] of Object.entries(updates)) {
    (firestoreData.fields as any)[key] = toFirestoreValue(value);
  }

  // Create field mask for partial updates
  const fieldPaths = Object.keys(updates);
  const fieldMask = fieldPaths.join(",");

  const url = `${BASE_URL}/${collection}/${documentId}?updateMask.fieldPaths=${fieldMask}`;

  const response = await fetch(url, {
    method: "PATCH",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(firestoreData),
  });

  if (!response.ok) {
    const errorData = await response.text();
    console.error("❌ REST API update error:", response.status, errorData);
    throw new Error(`Failed to update document: ${response.status}`);
  }

  console.log(`✅ REST API: Document updated: ${documentId}`);
};

// DELETE: Delete a document
export const deleteDocument = async (
  collection: string,
  documentId: string
): Promise<void> => {
  const token = getAuthToken();
  if (!token) {
    throw new Error("No authentication token available");
  }

  console.log(
    `📡 REST API: Deleting document ${documentId} from ${collection}`
  );

  const url = `${BASE_URL}/${collection}/${documentId}`;

  const response = await fetch(url, {
    method: "DELETE",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorData = await response.text();
    console.error("❌ REST API delete error:", response.status, errorData);
    throw new Error(`Failed to delete document: ${response.status}`);
  }

  console.log(`✅ REST API: Document deleted: ${documentId}`);
};

// QUERY: Get documents from a collection with filters
export const queryDocuments = async (
  collection: string,
  filters?: {
    field: string;
    operator: "==" | "!=" | "<" | "<=" | ">" | ">=";
    value: any;
  }[],
  orderBy?: {
    field: string;
    direction: "asc" | "desc";
  }[],
  limit?: number
): Promise<any[]> => {
  const token = getAuthToken();
  if (!token) {
    throw new Error("No authentication token available");
  }

  console.log(`📡 REST API: Querying collection ${collection}`, {
    filters,
    orderBy,
    limit,
  });

  // Build structured query
  const structuredQuery: any = {
    from: [{ collectionId: collection }],
  };

  // Add filters (where clauses)
  if (filters && filters.length > 0) {
    const whereFilters = filters.map((filter) => ({
      fieldFilter: {
        field: { fieldPath: filter.field },
        op:
          filter.operator === "=="
            ? "EQUAL"
            : filter.operator === "!="
            ? "NOT_EQUAL"
            : filter.operator === "<"
            ? "LESS_THAN"
            : filter.operator === "<="
            ? "LESS_THAN_OR_EQUAL"
            : filter.operator === ">"
            ? "GREATER_THAN"
            : filter.operator === ">="
            ? "GREATER_THAN_OR_EQUAL"
            : "EQUAL",
        value: toFirestoreValue(filter.value),
      },
    }));

    if (whereFilters.length === 1) {
      structuredQuery.where = whereFilters[0];
    } else {
      structuredQuery.where = {
        compositeFilter: {
          op: "AND",
          filters: whereFilters,
        },
      };
    }
  }

  // Add ordering
  if (orderBy && orderBy.length > 0) {
    structuredQuery.orderBy = orderBy.map((order) => ({
      field: { fieldPath: order.field },
      direction: order.direction === "asc" ? "ASCENDING" : "DESCENDING",
    }));
  }

  // Add limit
  if (limit) {
    structuredQuery.limit = limit;
  }

  const url = `${BASE_URL}:runQuery`;

  const response = await fetch(url, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ structuredQuery }),
  });

  if (!response.ok) {
    const errorData = await response.text();
    console.error("❌ REST API query error:", response.status, errorData);
    throw new Error(`Failed to query documents: ${response.status}`);
  }

  const result = await response.json();
  const documents = (result || [])
    .filter((item: any) => item.document)
    .map((item: any) => fromFirestoreDocument(item.document));

  console.log(`✅ REST API: Query returned ${documents.length} documents`);
  return documents;
};
