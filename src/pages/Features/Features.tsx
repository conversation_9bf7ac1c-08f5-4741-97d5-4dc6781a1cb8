import React from "react";
import Button from "../../components/ui/Button";
import "./Features.css";

const Features: React.FC = () => {
  const showcaseFeatures = [
    {
      id: "kiosk",
      title: "Tablet Kiosk Interface",
      subtitle: "Simple, touch-optimized task completion",
      description:
        "Perfect for retail stores, cafes, and service businesses. Staff can quickly see what needs to be done and mark tasks complete with a simple tap. No individual logins required - just walk up and get to work.",
      benefits: [
        "No per-user licensing costs",
        "Instant task visibility",
        "Role-based task filtering",
        "Touch-optimized interface",
      ],
      screenshotPath: "/images/screenshots/kiosk-interface.png",
      screenshotAlt:
        "Kiosk interface showing daily tasks organized by routine with completion progress",
    },
    {
      id: "scheduling",
      title: "Routine Scheduling",
      subtitle: "Automated task generation that works for you",
      description:
        "Create routines like 'Open Store' or 'Close Store' with specific days and times. Tasks automatically appear when needed, ensuring nothing gets forgotten during busy periods.",
      benefits: [
        "Automated task generation",
        "Flexible scheduling options",
        "Location-specific routines",
        "Easy schedule management",
      ],
      screenshotPath: "/images/screenshots/routine-schedules.png",
      screenshotAlt:
        "Routine schedules page showing weekly schedules for different store routines",
    },
    {
      id: "tasks",
      title: "Smart Task Organization",
      subtitle: "Organized by routines, not random lists",
      description:
        "Tasks are grouped into logical routines like 'Opening Procedures' or 'Maintenance Tasks' with clear priorities and deadlines. No more hunting through endless task lists.",
      benefits: [
        "Routine-based grouping",
        "Priority levels",
        "Expiry management",
        "Role assignments",
      ],
      screenshotPath: "/images/screenshots/routine-tasks.png",
      screenshotAlt:
        "Routine tasks page showing tasks organized by routine with priority indicators",
    },
    {
      id: "dashboard",
      title: "Manager Dashboard",
      subtitle: "Complete oversight across all locations",
      description:
        "Monitor task completion across all your locations in real-time. Track performance, identify bottlenecks, and ensure nothing falls through the cracks - all from one central dashboard.",
      benefits: [
        "Real-time updates",
        "Multi-location view",
        "Completion tracking",
        "Performance insights",
      ],
      screenshotPath: "/images/screenshots/manager-dashboard.png",
      screenshotAlt:
        "Today's tasks dashboard showing task completion status across multiple locations",
    },
  ];

  const additionalFeatures = [
    {
      icon: "🏢",
      title: "Multi-Location Management",
      description: "Centralized control across all your business locations",
      benefits: [
        "Location-specific settings",
        "Centralized oversight",
        "Manager assignments",
      ],
    },
    {
      icon: "👥",
      title: "Role-Based Access",
      description: "Different interfaces for owners, managers, and staff",
      benefits: [
        "Appropriate access levels",
        "Simplified interfaces",
        "Security controls",
      ],
    },
    {
      icon: "📈",
      title: "Performance Tracking",
      description:
        "Monitor completion rates and identify improvement opportunities",
      benefits: [
        "Completion metrics",
        "Trend analysis",
        "Accountability tools",
      ],
    },
    {
      icon: "🔄",
      title: "Seamless Handoffs",
      description: "Smooth shift transitions with notes and task summaries",
      benefits: ["Shift communication", "Task continuity", "Issue tracking"],
    },
  ];

  return (
    <div className="features">
      <section className="features__hero">
        <div className="features__hero-content">
          <h1>See AdminBuddy in Action</h1>
          <p>
            Discover how AdminBuddy transforms task management for small
            businesses with intuitive interfaces designed for real-world
            operations.
          </p>
        </div>
      </section>

      {/* Feature Showcase Section */}
      <section className="features__showcase">
        <div className="features__showcase-container">
          {showcaseFeatures.map((feature, index) => (
            <div
              key={feature.id}
              className={`features__showcase-item ${
                index % 2 === 1 ? "features__showcase-item--reverse" : ""
              }`}
            >
              <div className="features__showcase-content">
                <h2>{feature.title}</h2>
                <h3 className="features__showcase-subtitle">
                  {feature.subtitle}
                </h3>
                <p className="features__showcase-description">
                  {feature.description}
                </p>
                <ul className="features__showcase-benefits">
                  {feature.benefits.map((benefit, i) => (
                    <li key={i}>{benefit}</li>
                  ))}
                </ul>
              </div>
              <div className="features__showcase-image">
                <img
                  src={feature.screenshotPath}
                  alt={feature.screenshotAlt}
                  className="features__screenshot"
                  onError={(e) => {
                    // Fallback to placeholder if image doesn't exist
                    const target = e.target as HTMLImageElement;
                    target.style.display = "none";
                    const placeholder =
                      target.nextElementSibling as HTMLElement;
                    if (placeholder) placeholder.style.display = "flex";
                  }}
                />
                <div
                  className="features__screenshot-placeholder"
                  style={{ display: "none" }}
                >
                  <span className="features__screenshot-text">
                    Screenshot: {feature.screenshotAlt}
                  </span>
                  <small className="features__screenshot-note">
                    Coming soon - {feature.screenshotPath.split("/").pop()}
                  </small>
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Additional Features Grid */}
      <section className="features__grid">
        <div className="features__grid-header">
          <h2>Everything You Need to Succeed</h2>
          <p>
            Additional features that make AdminBuddy the complete solution for
            your business
          </p>
        </div>
        <div className="features__container">
          {additionalFeatures.map((feature, index) => (
            <div key={index} className="features__card">
              <div className="features__card-icon">{feature.icon}</div>
              <h3>{feature.title}</h3>
              <p>{feature.description}</p>
              <ul className="features__benefits">
                {feature.benefits.map((benefit, i) => (
                  <li key={i}>{benefit}</li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </section>

      <section className="features__cta">
        <div className="features__cta-content">
          <h2>Ready to streamline your operations?</h2>
          <p>
            Start your free trial today and see the difference organized task
            management makes.
          </p>
          <div className="features__cta-buttons">
            <Button variant="primary" size="large" href="/signup">
              Start Free Trial
            </Button>
            <Button variant="secondary" size="large" href="/pricing">
              View Pricing
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Features;
