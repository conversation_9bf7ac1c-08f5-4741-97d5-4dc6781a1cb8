import React, { useState, useEffect } from "react";
import { loadStripe } from "@stripe/stripe-js";
import { STRIPE_CONFIG } from "../../config/firebase";
import { useTenant } from "../../contexts/TenantContext";
import { getAllLocations } from "../../services/adminBuddyFirebaseService";
import "./BillingPage.css";

// Initialize Stripe
const stripePromise = loadStripe(STRIPE_CONFIG.publishableKey);

interface BillingPageProps {}

const BillingPage: React.FC<BillingPageProps> = () => {
  const { userProfile } = useTenant();
  const [locations, setLocations] = useState<any[]>([]);
  const [selectedPlan, setSelectedPlan] = useState<"monthly" | "yearly">(
    "monthly"
  );
  const [selectedLocationCount, setSelectedLocationCount] = useState(1);
  const [loading, setLoading] = useState(false);
  const [loadingLocations, setLoadingLocations] = useState(true);

  // Load locations to calculate pricing
  useEffect(() => {
    const loadLocations = async () => {
      if (!userProfile?.tenantId) return;

      try {
        setLoadingLocations(true);
        const locationData = await getAllLocations(userProfile.tenantId);
        setLocations(locationData);
      } catch (error) {
        console.error("Error loading locations:", error);
      } finally {
        setLoadingLocations(false);
      }
    };

    loadLocations();
  }, [userProfile?.tenantId]);

  const currentLocationCount = locations.length || 0;
  const monthlyPrice = selectedLocationCount * 20;
  const yearlyPrice = selectedLocationCount * 220;
  const yearlySavings = monthlyPrice * 12 - yearlyPrice;

  const handleSubscribe = async () => {
    if (!userProfile?.tenantId) {
      alert("Please log in to subscribe");
      return;
    }

    setLoading(true);

    try {
      const stripe = await stripePromise;
      if (!stripe) {
        throw new Error("Stripe failed to load");
      }

      // Create checkout session
      const response = await fetch(
        "https://us-central1-adminbuddy.cloudfunctions.net/createCheckoutSession",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            priceId: STRIPE_CONFIG.priceIds[selectedPlan],
            quantity: selectedLocationCount,
            tenantId: userProfile.tenantId,
            customerEmail: userProfile.email,
          }),
        }
      );

      const session = await response.json();

      if (session.error) {
        throw new Error(session.error);
      }

      // Redirect to Stripe Checkout
      const result = await stripe.redirectToCheckout({
        sessionId: session.sessionId,
      });

      if (result.error) {
        throw new Error(result.error.message);
      }
    } catch (error) {
      console.error("Error creating checkout session:", error);
      alert("Failed to start checkout. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  if (loadingLocations) {
    return (
      <div className="billing-page">
        <div className="billing-container">
          <div className="loading-state">
            <h2>Loading billing information...</h2>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="billing-page">
      <div className="billing-container">
        <div className="billing-header">
          <h1>Choose Your AdminBuddy Plan</h1>
          <p>
            Select the billing cycle and number of locations for your business
          </p>
          <div className="location-info">
            <span className="current-locations">
              Current: {currentLocationCount} location
              {currentLocationCount !== 1 ? "s" : ""}
            </span>
          </div>

          <div className="location-selector">
            <label htmlFor="locationCount">
              Number of locations to purchase:
            </label>
            <select
              id="locationCount"
              value={selectedLocationCount}
              onChange={(e) =>
                setSelectedLocationCount(parseInt(e.target.value))
              }
              className="location-select"
            >
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((count) => (
                <option key={count} value={count}>
                  {count} location{count !== 1 ? "s" : ""}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="pricing-cards">
          {/* Monthly Plan */}
          <div
            className={`pricing-card ${
              selectedPlan === "monthly" ? "selected" : ""
            }`}
          >
            <div className="plan-header">
              <h3>Monthly Billing</h3>
              <div className="price">
                <span className="currency">$</span>
                <span className="amount">{monthlyPrice}</span>
                <span className="period">/month</span>
              </div>
              <div className="price-breakdown">
                ${20} × {selectedLocationCount} location
                {selectedLocationCount !== 1 ? "s" : ""}
              </div>
            </div>
            <ul className="plan-features">
              <li>✅ Full task coordination system</li>
              <li>✅ Multi-location support</li>
              <li>✅ Real-time reporting</li>
              <li>✅ Kiosk interface</li>
              <li>✅ Email support</li>
            </ul>
            <button
              className={`plan-button ${
                selectedPlan === "monthly" ? "selected" : ""
              }`}
              onClick={() => setSelectedPlan("monthly")}
            >
              {selectedPlan === "monthly" ? "Selected" : "Select Monthly"}
            </button>
          </div>

          {/* Yearly Plan */}
          <div
            className={`pricing-card ${
              selectedPlan === "yearly" ? "selected" : ""
            }`}
          >
            <div className="plan-header">
              <div className="savings-badge">Save ${yearlySavings}/year</div>
              <h3>Yearly Billing</h3>
              <div className="price">
                <span className="currency">$</span>
                <span className="amount">{yearlyPrice}</span>
                <span className="period">/year</span>
              </div>
              <div className="price-breakdown">
                ${220} × {selectedLocationCount} location
                {selectedLocationCount !== 1 ? "s" : ""} (2 month free!)
              </div>
            </div>
            <ul className="plan-features">
              <li>✅ Everything in Monthly</li>
              <li>✅ 1 month free</li>
              <li>✅ Priority support</li>
            </ul>
            <button
              className={`plan-button ${
                selectedPlan === "yearly" ? "selected" : ""
              }`}
              onClick={() => setSelectedPlan("yearly")}
            >
              {selectedPlan === "yearly" ? "Selected" : "Select Yearly"}
            </button>
          </div>
        </div>

        <div className="billing-actions">
          <button
            className="subscribe-button"
            onClick={handleSubscribe}
            disabled={loading}
          >
            {loading
              ? "Processing..."
              : `Subscribe ${
                  selectedPlan === "monthly" ? "Monthly" : "Yearly"
                }`}
          </button>

          <div className="billing-notes">
            <p>• Cancel anytime</p>
            <p>• Secure payment processing by Stripe</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BillingPage;
