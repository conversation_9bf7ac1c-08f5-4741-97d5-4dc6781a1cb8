import React, { useState, useEffect } from "react";
import { loadStripe } from "@stripe/stripe-js";
import { STRIPE_CONFIG } from "../../config/firebase";
import { useTenant } from "../../contexts/TenantContext";
import { getAllLocations } from "../../services/adminBuddyFirebaseService";
import { getSubscriptionSummary } from "../../services/subscriptionService";
import {
  activateSubscriptionForTenant,
  fixUserProfile,
} from "../../services/manualSubscriptionService";
import TrialBanner from "../../components/TrialBanner/TrialBanner";
import "./BillingPage.css";

// Initialize Stripe
const stripePromise = loadStripe(STRIPE_CONFIG.publishableKey);

interface BillingPageProps {}

const BillingPage: React.FC<BillingPageProps> = () => {
  const { userProfile } = useTenant();
  const [locations, setLocations] = useState<any[]>([]);
  const [selectedPlan, setSelectedPlan] = useState<"monthly" | "yearly">(
    "monthly"
  );
  const [selectedLocationCount, setSelectedLocationCount] = useState(1);
  const [loading, setLoading] = useState(false);
  const [loadingLocations, setLoadingLocations] = useState(true);
  const [subscriptionSummary, setSubscriptionSummary] = useState<any>(null);

  // Load locations and subscription data
  useEffect(() => {
    const loadData = async () => {
      if (!userProfile?.tenantId) return;

      try {
        setLoadingLocations(true);

        // Load locations
        const locationData = await getAllLocations(userProfile.tenantId);
        setLocations(locationData);

        // Load subscription summary
        const subSummary = await getSubscriptionSummary(userProfile.tenantId);
        setSubscriptionSummary(subSummary);

        // Set default selected count based on current subscription or locations
        if (subSummary.hasSubscription && subSummary.locationCount > 0) {
          setSelectedLocationCount(subSummary.locationCount);
        } else {
          setSelectedLocationCount(Math.max(1, locationData.length || 1));
        }
      } catch (error) {
        console.error("Error loading billing data:", error);
        setLocations([]);
        setSelectedLocationCount(1);
        setSubscriptionSummary(null);
      } finally {
        setLoadingLocations(false);
      }
    };

    loadData();
  }, [userProfile?.tenantId]);

  const currentLocationCount = locations.length || 0;
  const monthlyPrice = selectedLocationCount * 20;
  const yearlyPrice = selectedLocationCount * 220;
  const yearlySavings = monthlyPrice * 12 - yearlyPrice;

  const handleSubscribe = async () => {
    if (!userProfile?.tenantId) {
      alert("Please log in to subscribe");
      return;
    }

    setLoading(true);

    try {
      const stripe = await stripePromise;
      if (!stripe) {
        throw new Error("Stripe failed to load");
      }

      // Create checkout session
      const response = await fetch(
        "https://us-central1-adminbuddy.cloudfunctions.net/createCheckoutSession",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            priceId: STRIPE_CONFIG.priceIds[selectedPlan],
            quantity: selectedLocationCount,
            tenantId: userProfile.tenantId,
            customerEmail: userProfile.email,
          }),
        }
      );

      const session = await response.json();

      if (session.error) {
        throw new Error(session.error);
      }

      // Redirect to Stripe Checkout
      const result = await stripe.redirectToCheckout({
        sessionId: session.sessionId,
      });

      if (result.error) {
        throw new Error(result.error.message);
      }
    } catch (error) {
      console.error("Error creating checkout session:", error);
      alert("Failed to start checkout. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Manual subscription activation for testing
  const handleManualActivation = async (locationCount: number) => {
    if (!userProfile?.tenantId) {
      alert("User information not available");
      return;
    }

    try {
      setLoading(true);
      console.log("🔧 Manually activating subscription...");

      await activateSubscriptionForTenant(userProfile.tenantId, locationCount);

      alert(
        `✅ Subscription activated! You now have ${locationCount} locations.`
      );

      // Refresh the page to show updated subscription status
      window.location.reload();
    } catch (error) {
      console.error("❌ Manual activation error:", error);
      alert(
        `Manual activation failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    } finally {
      setLoading(false);
    }
  };

  // Fix corrupted user profile
  const handleFixProfile = async () => {
    try {
      setLoading(true);
      console.log("🔧 Fixing user profile...");

      // Use known values from the logs
      const userId = "hk9iwb6Z0lR9g6MDqtlSY5zaZA53";
      const email = "<EMAIL>";
      const tenantId = "tenant-1749039378827-ta03hqi75";

      await fixUserProfile(userId, email, tenantId);

      alert("✅ User profile fixed! Please refresh the page.");

      // Refresh the page
      window.location.reload();
    } catch (error) {
      console.error("❌ Profile fix error:", error);
      alert(
        `Profile fix failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    } finally {
      setLoading(false);
    }
  };

  if (loadingLocations) {
    return (
      <div className="billing-page">
        <div className="billing-container">
          <div className="loading-state">
            <h2>Loading billing information...</h2>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="billing-page">
      <div className="billing-container">
        {/* Trial Banner */}
        <TrialBanner dismissible={false} />

        {/* Current Subscription Status */}
        {subscriptionSummary?.hasSubscription && (
          <div className="subscription-status">
            <h3>Current Subscription</h3>
            <div className="subscription-details">
              <p>
                <strong>Status:</strong> {subscriptionSummary.status}
              </p>
              <p>
                <strong>Locations:</strong> {subscriptionSummary.locationCount}
              </p>
              {subscriptionSummary.currentPeriodEnd && (
                <p>
                  <strong>Next billing:</strong>{" "}
                  {new Date(
                    subscriptionSummary.currentPeriodEnd
                  ).toLocaleDateString()}
                </p>
              )}
            </div>
          </div>
        )}

        <div className="billing-header">
          <h1>Choose Your AdminBuddy Plan</h1>
          <p>
            Select the billing cycle and number of locations for your business
          </p>
          <div className="location-info">
            <span className="current-locations">
              Current: {currentLocationCount} location
              {currentLocationCount !== 1 ? "s" : ""}
            </span>
          </div>

          <div className="location-selector">
            <label htmlFor="locationCount">
              Number of locations to purchase:
            </label>
            <select
              id="locationCount"
              value={selectedLocationCount}
              onChange={(e) =>
                setSelectedLocationCount(parseInt(e.target.value))
              }
              className="location-select"
            >
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((count) => (
                <option key={count} value={count}>
                  {count} location{count !== 1 ? "s" : ""}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="pricing-cards">
          {/* Monthly Plan */}
          <div
            className={`pricing-card ${
              selectedPlan === "monthly" ? "selected" : ""
            }`}
          >
            <div className="plan-header">
              <h3>Monthly Billing</h3>
              <div className="price">
                <span className="currency">$</span>
                <span className="amount">{monthlyPrice}</span>
                <span className="period">/month</span>
              </div>
              <div className="price-breakdown">
                ${20} × {selectedLocationCount} location
                {selectedLocationCount !== 1 ? "s" : ""}
              </div>
            </div>
            <ul className="plan-features">
              <li>✅ Full task coordination system</li>
              <li>✅ Multi-location support</li>
              <li>✅ Real-time reporting</li>
              <li>✅ Kiosk interface</li>
              <li>✅ Email support</li>
            </ul>
            <button
              className={`plan-button ${
                selectedPlan === "monthly" ? "selected" : ""
              }`}
              onClick={() => setSelectedPlan("monthly")}
            >
              {selectedPlan === "monthly" ? "Selected" : "Select Monthly"}
            </button>
          </div>

          {/* Yearly Plan */}
          <div
            className={`pricing-card ${
              selectedPlan === "yearly" ? "selected" : ""
            }`}
          >
            <div className="plan-header">
              <div className="savings-badge">Save ${yearlySavings}/year</div>
              <h3>Yearly Billing</h3>
              <div className="price">
                <span className="currency">$</span>
                <span className="amount">{yearlyPrice}</span>
                <span className="period">/year</span>
              </div>
              <div className="price-breakdown">
                ${220} × {selectedLocationCount} location
                {selectedLocationCount !== 1 ? "s" : ""} (1 month free!)
              </div>
            </div>
            <ul className="plan-features">
              <li>✅ Everything in Monthly</li>
              <li>✅ 1 month free</li>
              <li>✅ Priority support</li>
            </ul>
            <button
              className={`plan-button ${
                selectedPlan === "yearly" ? "selected" : ""
              }`}
              onClick={() => setSelectedPlan("yearly")}
            >
              {selectedPlan === "yearly" ? "Selected" : "Select Yearly"}
            </button>
          </div>
        </div>

        <div className="billing-actions">
          <button
            className="subscribe-button"
            onClick={handleSubscribe}
            disabled={loading}
          >
            {loading
              ? "Processing..."
              : `Subscribe ${
                  selectedPlan === "monthly" ? "Monthly" : "Yearly"
                }`}
          </button>

          {/* Temporary manual activation for testing */}
          <div
            className="manual-activation"
            style={{
              marginTop: "20px",
              padding: "20px",
              backgroundColor: "#fff3cd",
              border: "1px solid #ffeaa7",
              borderRadius: "8px",
            }}
          >
            <h4 style={{ color: "#856404", margin: "0 0 10px 0" }}>
              🔧 Testing: Manual Subscription Activation
            </h4>
            <p
              style={{
                color: "#856404",
                fontSize: "14px",
                margin: "0 0 15px 0",
              }}
            >
              If Stripe webhooks aren't working, use this to manually activate
              your subscription:
            </p>
            <div style={{ display: "flex", gap: "10px", flexWrap: "wrap" }}>
              <button
                onClick={() => handleManualActivation(1)}
                disabled={loading}
                style={{
                  padding: "8px 16px",
                  backgroundColor: "#28a745",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  cursor: "pointer",
                }}
              >
                Activate 1 Location
              </button>
              <button
                onClick={() => handleManualActivation(3)}
                disabled={loading}
                style={{
                  padding: "8px 16px",
                  backgroundColor: "#007bff",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  cursor: "pointer",
                }}
              >
                Activate 3 Locations
              </button>
              <button
                onClick={() => handleManualActivation(5)}
                disabled={loading}
                style={{
                  padding: "8px 16px",
                  backgroundColor: "#6f42c1",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  cursor: "pointer",
                }}
              >
                Activate 5 Locations
              </button>
            </div>

            {/* Fix corrupted profile section */}
            <div
              style={{
                marginTop: "15px",
                paddingTop: "15px",
                borderTop: "1px solid #ffeaa7",
              }}
            >
              <h5 style={{ color: "#856404", margin: "0 0 10px 0" }}>
                🚨 Profile Corrupted? Fix It Here:
              </h5>
              <p
                style={{
                  color: "#856404",
                  fontSize: "12px",
                  margin: "0 0 10px 0",
                }}
              >
                If you're logged out or missing profile data, click below:
              </p>
              <button
                onClick={handleFixProfile}
                disabled={loading}
                style={{
                  padding: "8px 16px",
                  backgroundColor: "#dc3545",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  cursor: "pointer",
                  fontSize: "14px",
                }}
              >
                🔧 Fix User Profile
              </button>
            </div>
          </div>

          <div className="billing-notes">
            <p>• Cancel anytime</p>
            <p>• Secure payment processing by Stripe</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BillingPage;
