import React, { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import "./KioskInterface.css";
import {
  getAllLocations,
  getAllDailyTasks,
  getAllAdHocTasks,
  updateDailyTaskStatus,
  updateAdHocTaskStatus,
  updateAdHocTask,
  getAllRoutines,
  getAllRoutineTasks,
  type Location,
  type Routine,
  type RoutineTask,
} from "../../services/adminBuddyFirebaseService";

// Combined Task interface for kiosk functionality
interface KioskTask {
  id: string;
  title: string;
  description?: string;
  priority: "low" | "medium" | "high";
  requiredRole?: string;
  dueTime?: string;
  status: "pending" | "completed" | "expired";
  type: "daily" | "adhoc";
  locationId: string;
  notes?: string;
  routineName?: string; // For grouping by routine
  routineTaskId?: string; // For daily tasks
  expiryType?: "next_report" | "set_datetime" | "no_expiry"; // For expiry logic
}

const KioskInterface: React.FC = () => {
  const { locationId } = useParams<{ locationId?: string }>();
  const [currentLocation, setCurrentLocation] = useState<Location | null>(null);
  const [availableLocations, setAvailableLocations] = useState<Location[]>([]);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [tasks, setTasks] = useState<KioskTask[]>([]);
  const [routines, setRoutines] = useState<Routine[]>([]);
  const [routineTasks, setRoutineTasks] = useState<RoutineTask[]>([]);
  const [tenantRoles, setTenantRoles] = useState<any[]>([]);
  const [filter, setFilter] = useState<"all" | "pending" | "completed">(
    "pending"
  );
  const [viewMode, setViewMode] = useState<"routine" | "role">("routine");
  const [selectedPriority, setSelectedPriority] = useState<string>("all");
  const [currentTime, setCurrentTime] = useState(new Date());
  const [showTaskModal, setShowTaskModal] = useState(false);
  const [selectedTask, setSelectedTask] = useState<KioskTask | null>(null);
  const [taskNote, setTaskNote] = useState("");
  const [showEndDayModal, setShowEndDayModal] = useState(false);
  const [endDayNotes, setEndDayNotes] = useState("");
  const [authError, setAuthError] = useState<string | null>(null);

  // Helper function to handle auth errors and redirect to login
  const handleAuthError = (error: any, context: string) => {
    console.error(`❌ Kiosk: Error in ${context}:`, error);

    // Check if it's specifically an auth-related error (not index or other errors)
    const isAuthError =
      error?.code === "permission-denied" ||
      error?.code === "unauthenticated" ||
      error?.message?.includes("UNAUTHENTICATED") ||
      error?.message?.includes("PERMISSION_DENIED") ||
      (error?.message?.includes("auth") &&
        !error?.message?.includes("index")) ||
      (error?.message?.includes("token") && !error?.message?.includes("index"));

    if (isAuthError) {
      console.log("🔐 Kiosk: Auth error detected, redirecting to login...");
      setAuthError("Your session has expired. Redirecting to login...");

      // Store current URL for redirect after login
      const currentUrl = window.location.pathname + window.location.search;
      localStorage.setItem("adminbuddy_redirect_after_login", currentUrl);
      console.log("💾 Stored redirect URL:", currentUrl);

      // Clear auth data and redirect after a short delay
      setTimeout(() => {
        localStorage.removeItem("adminbuddy_auth");
        window.location.href = "/login";
      }, 2000);

      return true; // Indicates auth error was handled
    }

    return false; // Not an auth error
  };

  // Load user authentication and locations
  useEffect(() => {
    const loadUserAndLocations = async () => {
      try {
        console.log("🔐 Kiosk: Loading user authentication...");

        // Check if we have auth data from our REST API login
        const authData = localStorage.getItem("adminbuddy_auth");

        if (!authData) {
          console.log("❌ Kiosk: No auth data found - user must login first");
          setAuthError("No authentication found. Redirecting to login...");
          setTimeout(() => {
            window.location.href = "/login";
          }, 2000);
          return;
        }

        const { uid, email } = JSON.parse(authData);
        console.log("👤 Kiosk: Found auth data for:", email);

        // Get fresh user profile from Firestore (don't trust localStorage for profile data)
        const { getUserProfile } = await import(
          "../../services/adminBuddyFirebaseService"
        );
        const userProfile = await getUserProfile(uid);

        if (!userProfile) {
          console.log("❌ Kiosk: Could not load user profile from Firestore");
          setAuthError("Could not load user profile. Redirecting to login...");
          setTimeout(() => {
            localStorage.removeItem("adminbuddy_auth");
            window.location.href = "/login";
          }, 2000);
          return;
        }

        console.log("👤 Kiosk: Loaded fresh user profile:", {
          email: userProfile.email,
          tenantId: userProfile.tenantId,
        });

        if (!userProfile.tenantId) {
          console.log(
            "❌ Kiosk: User profile has no tenant ID - invalid user profile"
          );
          setAuthError("Invalid user profile. Redirecting to login...");
          setTimeout(() => {
            localStorage.removeItem("adminbuddy_auth");
            window.location.href = "/login";
          }, 2000);
          return;
        }

        setCurrentUser(userProfile);

        // Get all locations for this tenant (with proper tenant filtering)
        console.log(
          "🏢 Kiosk: Loading locations for tenant:",
          userProfile.tenantId
        );
        const tenantLocations = await getAllLocations(userProfile.tenantId);
        const activeLocations = tenantLocations;
        setAvailableLocations(activeLocations);

        console.log(
          `✅ Kiosk: Found ${activeLocations.length} active locations for tenant ${userProfile.tenantId}`
        );
      } catch (error) {
        // Use the auth error handler
        if (!handleAuthError(error, "loadUserAndLocations")) {
          console.error(
            "❌ Kiosk: Non-auth error loading user/locations:",
            error
          );
        }
      }
    };

    loadUserAndLocations();
  }, []);

  // Function to reload tasks from server
  const reloadTasks = async () => {
    if (!currentUser?.tenantId || !locationId || !currentLocation) {
      console.log("⚠️ Cannot reload tasks - missing required data");
      return;
    }

    try {
      console.log("🔄 Reloading tasks from server...");

      // Get all tasks for this tenant+location
      const [dailyTasks, adHocTasks] = await Promise.all([
        getAllDailyTasks(currentUser.tenantId, undefined, locationId),
        getAllAdHocTasks(currentUser.tenantId, locationId),
      ]);

      // Convert tasks to kiosk format and filter out reported tasks
      const locationDailyTasks: KioskTask[] = dailyTasks
        .filter((task) => !task.reportedAt)
        .map((task) => {
          const routineTask = routineTasks.find(
            (rt) => rt.id === task.routineTaskId
          );
          const routine = routineTask
            ? routines.find((r) => r.id === routineTask.routineId)
            : null;

          return {
            id: task.id,
            title: task.title,
            description: task.description,
            priority: task.priority,
            requiredRole: task.requiredRole,
            dueTime: task.dueTime,
            status: task.status,
            type: "daily" as const,
            locationId: task.locationId,
            notes: task.notes,
            routineName: routine?.name || "Unknown Routine",
            routineTaskId: task.routineTaskId,
            expiryType: task.expiryType,
          };
        });

      const locationAdHocTasks: KioskTask[] = adHocTasks
        .filter((task) => !task.reportedAt)
        .map((task) => ({
          id: task.id,
          title: task.title,
          description: task.description,
          priority: task.priority,
          requiredRole: task.requiredRole,
          dueTime: task.dueTime,
          status: task.status,
          type: "adhoc" as const,
          locationId: task.locationId,
          notes: task.notes,
          routineName: "Ad-hoc Tasks",
          expiryType: task.expiryType,
        }));

      const allTasks = [...locationDailyTasks, ...locationAdHocTasks];
      setTasks(allTasks);

      console.log(`✅ Reloaded ${allTasks.length} tasks from server`);
    } catch (error) {
      console.error("❌ Error reloading tasks:", error);
    }
  };

  // Load specific location data when locationId is provided
  useEffect(() => {
    const loadLocationData = async () => {
      if (!locationId || !currentUser) return;

      try {
        console.log("🔥 Loading kiosk data for location:", locationId);

        // Find the location in our available locations
        const location = availableLocations.find(
          (loc) => loc.id === locationId
        );

        if (!location) {
          console.log("❌ Location not found or not accessible:", locationId);
          return;
        }

        setCurrentLocation(location);

        // Get all tasks, routines, and routine tasks for this tenant+location (no date filtering)
        const [dailyTasks, adHocTasks, routinesData, routineTasksData] =
          await Promise.all([
            getAllDailyTasks(location.tenantId, undefined, locationId), // No date filter - show all unreported tasks
            getAllAdHocTasks(location.tenantId, locationId),
            getAllRoutines(location.tenantId),
            getAllRoutineTasks(location.tenantId),
          ]);

        setRoutines(routinesData);
        setRoutineTasks(routineTasksData);

        // Convert tasks to kiosk format and filter out reported tasks
        const locationDailyTasks: KioskTask[] = dailyTasks
          .filter((task) => !task.reportedAt) // Exclude tasks that have been reported
          .map((task) => {
            // Find the routine task to get routine information
            const routineTask = routineTasksData.find(
              (rt) => rt.id === task.routineTaskId
            );
            const routine = routineTask
              ? routinesData.find((r) => r.id === routineTask.routineId)
              : null;

            return {
              id: task.id,
              title: task.title,
              description: task.description,
              priority: task.priority,
              requiredRole: task.requiredRole,
              dueTime: task.dueTime,
              status: task.status,
              type: "daily" as const,
              locationId: task.locationId,
              notes: task.notes,
              routineName: routine?.name || "Unknown Routine",
              routineTaskId: task.routineTaskId,
              expiryType: task.expiryType,
            };
          });

        // Filter ad-hoc tasks to exclude reported tasks (no date filtering)
        const locationAdHocTasks: KioskTask[] = adHocTasks
          .filter((task) => !task.reportedAt) // Exclude tasks that have been reported
          .map((task) => ({
            id: task.id,
            title: task.title,
            description: task.description,
            priority: task.priority,
            requiredRole: task.requiredRole,
            dueTime: task.dueTime,
            status: task.status,
            type: "adhoc" as const,
            locationId: task.locationId,
            notes: task.notes,
            routineName: "Ad-hoc Tasks", // Group all ad-hoc tasks together
            expiryType: task.expiryType,
          }));

        const allTasks = [...locationDailyTasks, ...locationAdHocTasks];
        setTasks(allTasks);

        // Debug: Log task statuses
        const pendingTasks = allTasks.filter((t) => t.status === "pending");
        const completedTasks = allTasks.filter((t) => t.status === "completed");
        const expiredTasks = allTasks.filter((t) => t.status === "expired");

        console.log(
          `✅ Loaded ${allTasks.length} tasks for location ${location.name} (${locationDailyTasks.length} daily, ${locationAdHocTasks.length} ad-hoc)`
        );
        console.log(`📊 Task status breakdown:`, {
          pending: pendingTasks.length,
          completed: completedTasks.length,
          expired: expiredTasks.length,
          total: allTasks.length,
        });

        // Debug: Log completed tasks specifically
        if (completedTasks.length > 0) {
          console.log(
            `✅ Found ${completedTasks.length} completed tasks:`,
            completedTasks.map((t) => ({
              id: t.id,
              title: t.title,
              status: t.status,
            }))
          );
        }
      } catch (error) {
        // Use the auth error handler
        if (!handleAuthError(error, "loadLocationData")) {
          console.error(
            "❌ Kiosk: Non-auth error loading location data:",
            error
          );
        }
      }
    };

    loadLocationData();
  }, [locationId, currentUser, availableLocations]);

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  const handleTaskClick = async (
    task: KioskTask,
    action: "complete" | "note"
  ) => {
    if (action === "complete") {
      // Toggle task completion and update in Firebase
      const newStatus = task.status === "completed" ? "pending" : "completed";

      try {
        // Update in Firebase
        if (task.type === "daily") {
          await updateDailyTaskStatus(task.id, newStatus);
        } else {
          await updateAdHocTaskStatus(task.id, newStatus);
        }

        // Update local state
        setTasks((prevTasks) =>
          prevTasks.map((t) =>
            t.id === task.id ? { ...t, status: newStatus } : t
          )
        );
      } catch (error) {
        // Use the auth error handler
        if (!handleAuthError(error, "updateTaskStatus")) {
          console.error("Error updating task status:", error);
          alert("Failed to update task. Please try again.");
        }
      }
    } else {
      // Open modal for notes
      setSelectedTask(task);
      setTaskNote(task.notes || "");
      setShowTaskModal(true);
    }
  };

  const handleTaskComplete = async () => {
    if (!selectedTask) return;

    try {
      // Update in Firebase first (including notes)
      const notes = taskNote.trim() || undefined;
      if (selectedTask.type === "daily") {
        await updateDailyTaskStatus(selectedTask.id, "completed", notes);
      } else {
        await updateAdHocTaskStatus(selectedTask.id, "completed", notes);
      }

      // Update local state
      setTasks((prevTasks) =>
        prevTasks.map((task) =>
          task.id === selectedTask.id
            ? {
                ...task,
                status: "completed",
                isCompleted: true,
                completedAt: new Date().toISOString(),
                completedBy: "Current User",
                notes: taskNote.trim() || undefined,
              }
            : task
        )
      );

      setShowTaskModal(false);
      setSelectedTask(null);
      setTaskNote("");
    } catch (error) {
      // Use the auth error handler
      if (!handleAuthError(error, "completeTask")) {
        console.error("Error completing task:", error);
        alert("Failed to complete task. Please try again.");
      }
    }
  };

  const handleTaskAddNote = async () => {
    if (!selectedTask) return;

    try {
      // Update notes in Firebase
      const notes = taskNote.trim() || undefined;
      if (selectedTask.type === "daily") {
        // For daily tasks, use updateDailyTaskStatus with current status to update notes
        await updateDailyTaskStatus(
          selectedTask.id,
          selectedTask.status,
          notes
        );
      } else {
        // For ad-hoc tasks, use updateAdHocTask to update just the notes
        await updateAdHocTask(selectedTask.id, { notes });
      }

      // Update local state
      setTasks((prevTasks) =>
        prevTasks.map((task) =>
          task.id === selectedTask.id
            ? {
                ...task,
                notes: taskNote.trim() || undefined,
              }
            : task
        )
      );

      setShowTaskModal(false);
      setSelectedTask(null);
      setTaskNote("");
    } catch (error) {
      // Use the auth error handler
      if (!handleAuthError(error, "addTaskNote")) {
        console.error("Error adding task note:", error);
        alert("Failed to save note. Please try again.");
      }
    }
  };

  const handleEndDay = async () => {
    if (!currentUser?.tenantId || !locationId || !currentLocation) {
      console.error("❌ Missing required data for report creation");
      return;
    }

    try {
      console.log("📊 Creating location report...");

      // Import the report creation function
      const { createLocationReport } = await import(
        "../../services/adminBuddyFirebaseService"
      );

      // Create the report
      const reportId = await createLocationReport(
        currentUser.tenantId,
        locationId,
        currentLocation.name,
        currentUser.email,
        endDayNotes
      );

      console.log("✅ Report created successfully:", reportId);

      // Reload tasks from server to get updated status (expired tasks will be gone)
      // Note: Tasks with expiryType "next_report" are automatically expired when the report is created
      await reloadTasks();
      console.log(
        "🔄 Reloaded tasks from server - expired and completed tasks should be gone"
      );

      // Show success message
      setShowEndDayModal(false);
      setEndDayNotes("");

      // Show a toast notification
      const toast = document.createElement("div");
      toast.className = "toast-notification";
      toast.innerHTML = "📊 Report sent successfully!";
      toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #10b981;
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        font-weight: 500;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      `;
      document.body.appendChild(toast);

      // Remove toast after 3 seconds
      setTimeout(() => {
        if (document.body.contains(toast)) {
          document.body.removeChild(toast);
        }
      }, 3000);
    } catch (error) {
      console.error("❌ Error creating report:", error);

      // Use the auth error handler first
      if (!handleAuthError(error, "createLocationReport")) {
        // Show error message for non-auth errors
        const toast = document.createElement("div");
        toast.className = "toast-notification";
        toast.innerHTML = "❌ Failed to send report. Please try again.";
        toast.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          background: #ef4444;
          color: white;
          padding: 12px 20px;
          border-radius: 8px;
          font-weight: 500;
          z-index: 10000;
          box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        document.body.appendChild(toast);

        // Remove toast after 5 seconds
        setTimeout(() => {
          if (document.body.contains(toast)) {
            document.body.removeChild(toast);
          }
        }, 5000);
      }
    }
  };

  const priorities = [
    "all",
    ...Array.from(new Set(tasks.map((task) => task.priority))),
  ];

  const filteredTasks = tasks.filter((task) => {
    const matchesFilter =
      filter === "all" ||
      (filter === "pending" && task.status === "pending") ||
      (filter === "completed" && task.status === "completed");

    const matchesPriority =
      selectedPriority === "all" || task.priority === selectedPriority;

    return matchesFilter && matchesPriority;
  });

  // Group tasks by routine or role based on view mode
  const groupedTasks = filteredTasks.reduce((groups, task) => {
    let groupKey: string;

    if (viewMode === "role") {
      // Group by role - capitalize first letter for display
      const role = task.requiredRole || "any employee";
      groupKey = role.charAt(0).toUpperCase() + role.slice(1);
    } else {
      // Group by routine (default)
      groupKey = task.routineName || "Unknown Routine";
    }

    if (!groups[groupKey]) {
      groups[groupKey] = [];
    }
    groups[groupKey].push(task);
    return groups;
  }, {} as Record<string, KioskTask[]>);

  const completedCount = tasks.filter(
    (task) => task.status === "completed"
  ).length;
  const totalCount = tasks.length;
  const pendingCount = tasks.filter((task) => task.status === "pending").length;

  // Fun empty state messages
  const getEmptyStateMessage = () => {
    const messages = [
      { title: "🎉 All done!", subtitle: "Woohoo!" },
      { title: "✨ You're crushing it!", subtitle: "Every task completed!" },
      { title: "🏆 Task master!", subtitle: "Nothing left on the list!" },
      { title: "🎯 Perfect score!", subtitle: "100% completion achieved!" },
      { title: "🌟 Stellar work!", subtitle: "All tasks knocked out!" },
    ];
    return messages[Math.floor(Math.random() * messages.length)];
  };

  // Show auth error if present
  if (authError) {
    return (
      <div className="kiosk">
        <div className="kiosk__loading">
          <h2>🔐 Authentication Error</h2>
          <p>{authError}</p>
          <div className="loading-spinner" style={{ marginTop: "1rem" }}>
            <div className="spinner"></div>
          </div>
        </div>
      </div>
    );
  }

  // Show location selector if no locationId provided
  if (!locationId) {
    if (!currentUser) {
      return (
        <div className="kiosk">
          <div className="kiosk__loading">
            <h2>🔐 Authentication Required</h2>
            <p>Please log in to access the kiosk interface.</p>
            <button
              className="btn btn--primary"
              onClick={() => (window.location.href = "/login")}
              style={{ marginTop: "1rem" }}
            >
              Go to Login
            </button>
          </div>
        </div>
      );
    }

    if (!currentUser.tenantId) {
      return (
        <div className="kiosk">
          <div className="kiosk__loading">
            <h2>🚫 Invalid User Profile</h2>
            <p>Your user profile is missing tenant information.</p>
            <p>Please contact your administrator or try logging in again.</p>
            <button
              className="btn btn--primary"
              onClick={() => {
                localStorage.removeItem("adminbuddy_auth");
                window.location.href = "/login";
              }}
              style={{ marginTop: "1rem" }}
            >
              Login Again
            </button>
          </div>
        </div>
      );
    }

    return (
      <div className="kiosk">
        <div className="kiosk__loading">
          <h2>🏢 Select Location</h2>
          <p>Choose a location to access the kiosk interface:</p>
          <div
            style={{
              fontSize: "0.9rem",
              color: "#6b7280",
              marginBottom: "1rem",
            }}
          >
            Logged in as: {currentUser.email} | Tenant: {currentUser.tenantId}
          </div>

          {availableLocations.length > 0 ? (
            <div className="location-selector">
              {availableLocations.map((location) => (
                <button
                  key={location.id}
                  className="location-btn"
                  onClick={() =>
                    (window.location.href = `/kiosk/${location.id}`)
                  }
                >
                  <div className="location-btn__content">
                    <h3>{location.name}</h3>
                    <p>{location.address}</p>
                    <div className="location-btn__meta">
                      <span className="location-type">📍 Location</span>
                    </div>
                  </div>
                  <div className="location-btn__arrow">→</div>
                </button>
              ))}
            </div>
          ) : (
            <div className="empty-locations">
              <p>No active locations found for your tenant.</p>
              <p>Please contact your administrator to set up locations.</p>
              <div
                style={{
                  marginTop: "1rem",
                  fontSize: "0.8rem",
                  color: "#9ca3af",
                }}
              >
                Tenant ID: {currentUser.tenantId}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  if (!currentLocation) {
    return (
      <div className="kiosk">
        <div className="kiosk__loading">
          <h2>Loading location...</h2>
        </div>
      </div>
    );
  }

  return (
    <div className="kiosk">
      {/* Compact Header */}
      <header className="kiosk__header-compact">
        <div className="kiosk__header-top">
          <div className="kiosk__location-compact">
            <h1>{currentLocation.name}</h1>
            <span className="kiosk__time-compact">
              {currentTime.toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              })}
            </span>
          </div>
          <button
            className="kiosk__logout-btn"
            onClick={() => {
              // Store current URL for redirect after login
              const currentUrl =
                window.location.pathname + window.location.search;
              localStorage.setItem(
                "adminbuddy_redirect_after_login",
                currentUrl
              );
              console.log(
                "💾 Manual logout - stored redirect URL:",
                currentUrl
              );

              localStorage.removeItem("adminbuddy_auth");
              window.location.href = "/login";
            }}
            title="Logout"
          >
            🔓
          </button>
        </div>
      </header>

      {/* Compact Controls */}
      <div className="kiosk__controls-compact">
        <div className="kiosk__progress-compact">
          <div className="kiosk__progress-bar">
            <div
              className="kiosk__progress-fill"
              style={{ width: `${(completedCount / totalCount) * 100}%` }}
            />
          </div>
          <span className="kiosk__progress-text">
            {completedCount}/{totalCount} completed
          </span>
        </div>

        <div className="kiosk__filter-group">
          <button
            className={`kiosk__filter-btn ${
              filter === "pending" ? "active" : ""
            }`}
            onClick={() => setFilter("pending")}
          >
            To Do ({tasks.filter((t) => t.status === "pending").length})
          </button>
          <button
            className={`kiosk__filter-btn ${
              filter === "completed" ? "active" : ""
            }`}
            onClick={() => setFilter("completed")}
          >
            Done ({completedCount})
          </button>
          <button
            className={`kiosk__filter-btn ${filter === "all" ? "active" : ""}`}
            onClick={() => setFilter("all")}
          >
            All ({totalCount})
          </button>
        </div>

        <div className="kiosk__view-toggle">
          <button
            className={`kiosk__view-btn ${
              viewMode === "routine" ? "active" : ""
            }`}
            onClick={() => setViewMode("routine")}
            title="Group by Routine"
          >
            📋 Routines
          </button>
          <button
            className={`kiosk__view-btn ${viewMode === "role" ? "active" : ""}`}
            onClick={() => setViewMode("role")}
            title="Group by Role"
          >
            👥 Roles
          </button>
        </div>

        <button
          className="kiosk__end-day-btn-compact"
          onClick={() => setShowEndDayModal(true)}
        >
          📊 Send Report
        </button>
      </div>

      {/* Task List - Grouped by Routine or Role */}
      <div className="kiosk__tasks">
        {filteredTasks.length === 0 ? (
          <div className="kiosk__empty">
            {pendingCount === 0 && filter === "pending" ? (
              (() => {
                const message = getEmptyStateMessage();
                return (
                  <>
                    <h3>{message.title}</h3>
                    <p>{message.subtitle}</p>
                  </>
                );
              })()
            ) : (
              <>
                <h3>No tasks found</h3>
                <p>Try adjusting your filters</p>
              </>
            )}
          </div>
        ) : (
          Object.entries(groupedTasks)
            .sort(([a], [b]) => a.localeCompare(b)) // Sort groups alphabetically
            .map(([groupName, groupTasks]) => (
              <div key={groupName} className="kiosk__routine-group">
                <h3 className="kiosk__routine-title">
                  {viewMode === "role" ? "👥 " : "📋 "}
                  {groupName}
                </h3>
                <div className="kiosk__routine-tasks">
                  {groupTasks.map((task) => (
                    <TaskCard
                      key={task.id}
                      task={task}
                      onTaskAction={handleTaskClick}
                      viewMode={viewMode}
                    />
                  ))}
                </div>
              </div>
            ))
        )}
      </div>

      {/* Task Modal */}
      {showTaskModal && selectedTask && (
        <div
          className="kiosk__modal-overlay"
          onClick={() => setShowTaskModal(false)}
        >
          <div className="kiosk__modal" onClick={(e) => e.stopPropagation()}>
            <div className="kiosk__modal-header">
              <h3>{selectedTask.title}</h3>
              <button
                className="kiosk__modal-close"
                onClick={() => setShowTaskModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="kiosk__modal-content">
              {selectedTask.description && (
                <p className="kiosk__modal-description">
                  {selectedTask.description}
                </p>
              )}

              <div className="kiosk__modal-field">
                <label htmlFor="taskNote">Add a note (optional):</label>
                <textarea
                  id="taskNote"
                  value={taskNote}
                  onChange={(e) => setTaskNote(e.target.value)}
                  placeholder="e.g., Partially completed, ran out of time..."
                  rows={3}
                />
              </div>
            </div>

            <div className="kiosk__modal-actions">
              <button
                className="kiosk__modal-btn kiosk__modal-btn--secondary"
                onClick={handleTaskAddNote}
              >
                💬 Add Note Only
              </button>
              <button
                className="kiosk__modal-btn kiosk__modal-btn--primary"
                onClick={handleTaskComplete}
              >
                ✅ Mark Complete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* End Day Modal */}
      {showEndDayModal && (
        <div
          className="kiosk__modal-overlay"
          onClick={() => setShowEndDayModal(false)}
        >
          <div className="kiosk__modal" onClick={(e) => e.stopPropagation()}>
            <div className="kiosk__modal-header">
              <h3>📊 Send Report</h3>
              <button
                className="kiosk__modal-close"
                onClick={() => setShowEndDayModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="kiosk__modal-content">
              <p>
                This will send a summary of completed tasks and current status
                to your manager.
              </p>

              <div className="kiosk__modal-field">
                <label htmlFor="endDayNotes">Report notes:</label>
                <textarea
                  id="endDayNotes"
                  value={endDayNotes}
                  onChange={(e) => setEndDayNotes(e.target.value)}
                  placeholder="Any issues, observations, or notes for the manager..."
                  rows={4}
                />
              </div>
            </div>

            <div className="kiosk__modal-actions">
              <button
                className="kiosk__modal-btn kiosk__modal-btn--secondary"
                onClick={() => setShowEndDayModal(false)}
              >
                Cancel
              </button>
              <button
                className="kiosk__modal-btn kiosk__modal-btn--primary"
                onClick={handleEndDay}
              >
                🚀 Send Report
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// TaskCard Component
interface TaskCardProps {
  task: KioskTask;
  onTaskAction: (task: KioskTask, action: "complete" | "note") => void;
  viewMode: "routine" | "role";
}

const TaskCard: React.FC<TaskCardProps> = ({
  task,
  onTaskAction,
  viewMode,
}) => {
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "#ef4444";
      case "medium":
        return "#f59e0b";
      case "low":
        return "#10b981";
      default:
        return "#6b7280";
    }
  };

  const formatTime = (timeString?: string) => {
    if (!timeString) return null;
    const [hours, minutes] = timeString.split(":");
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? "PM" : "AM";
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  const isOverdue = () => {
    if (!task.dueTime || task.status === "completed") return false;
    const now = new Date();
    const [hours, minutes] = task.dueTime.split(":");
    const dueDate = new Date();
    dueDate.setHours(parseInt(hours), parseInt(minutes), 0, 0);
    return now > dueDate;
  };

  const formatRoleName = (role?: string) => {
    if (!role) return "Any Employee";
    return role.charAt(0).toUpperCase() + role.slice(1);
  };

  return (
    <div
      className={`kiosk__task-card ${
        task.status === "completed" ? "completed" : ""
      } ${isOverdue() ? "overdue" : ""}`}
    >
      <div className="kiosk__task-content">
        <div className="kiosk__task-header">
          <div className="kiosk__task-title-row">
            <h3 className="kiosk__task-title">{task.title}</h3>
            {task.dueTime && (
              <span
                className={`kiosk__task-time ${isOverdue() ? "overdue" : ""}`}
              >
                {formatTime(task.dueTime)}
              </span>
            )}
          </div>

          <div className="kiosk__task-meta">
            <span
              className="kiosk__task-priority"
              style={{ backgroundColor: getPriorityColor(task.priority) }}
            >
              {task.priority}
            </span>
          </div>
        </div>

        {/* Show role when in routine view, routine when in role view */}
        {viewMode === "routine" && task.requiredRole && (
          <div className="kiosk__task-context">
            <span className="kiosk__task-context-label">👥 Role:</span>
            <span className="kiosk__task-context-value">
              {formatRoleName(task.requiredRole)}
            </span>
          </div>
        )}
        {viewMode === "role" && task.routineName && (
          <div className="kiosk__task-context">
            <span className="kiosk__task-context-label">📋 Routine:</span>
            <span className="kiosk__task-context-value">
              {task.routineName}
            </span>
          </div>
        )}

        {task.notes && <div className="kiosk__task-note">💬 {task.notes}</div>}

        <div className="kiosk__task-actions">
          {task.status === "completed" ? (
            <div className="kiosk__task-completed">
              <span className="kiosk__task-completed-by">
                ✅ Task Completed
              </span>
              <button
                className="kiosk__task-btn kiosk__task-btn--undo"
                onClick={(e) => {
                  e.stopPropagation();
                  onTaskAction(task, "complete");
                }}
              >
                Undo
              </button>
            </div>
          ) : (
            <div className="kiosk__task-buttons">
              <button
                className="kiosk__task-btn kiosk__task-btn--complete"
                onClick={(e) => {
                  e.stopPropagation();
                  onTaskAction(task, "complete");
                }}
              >
                ✅ Complete
              </button>
              <button
                className="kiosk__task-btn kiosk__task-btn--note"
                onClick={(e) => {
                  e.stopPropagation();
                  onTaskAction(task, "note");
                }}
              >
                💬 Note
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default KioskInterface;
