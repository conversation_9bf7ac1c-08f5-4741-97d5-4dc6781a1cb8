.login {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  padding: 2rem;
}

.login__container {
  background: white;
  border-radius: 12px;
  padding: 3rem;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.login__header {
  text-align: center;
  margin-bottom: 2rem;
}

.login__logo {
  font-size: 1.5rem;
  font-weight: bold;
  color: #0a2d69;
  text-decoration: none;
  display: block;
  margin-bottom: 1rem;
}

.login__header h1 {
  font-size: 2rem;
  color: #1a1a1a;
  margin-bottom: 0.5rem;
}

.login__header p {
  color: #666;
  margin: 0;
}

.login__form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.login__field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.login__field label {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.login__field input {
  padding: 0.75rem;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.login__field input:focus {
  outline: none;
  border-color: #0a2d69;
}

.login__error {
  background: #fee;
  color: #c33;
  padding: 0.75rem;
  border-radius: 8px;
  border: 1px solid #fcc;
  font-size: 0.9rem;
}

.login__submit {
  width: 100%;
  margin-top: 1rem;
}

.login__footer {
  text-align: center;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #eee;
}

.login__footer p {
  margin: 0.5rem 0;
  color: #666;
  font-size: 0.9rem;
}

.login__footer a {
  color: #0a2d69;
  text-decoration: none;
  font-weight: 500;
}

.login__footer a:hover {
  text-decoration: underline;
}

@media (max-width: 480px) {
  .login {
    padding: 1rem;
  }
  
  .login__container {
    padding: 2rem;
  }
}
