import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { signInUser } from "../../services/userService";
import Button from "../../components/ui/Button";
import "./Login.css";

const Login: React.FC = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      const user = await signInUser(email, password);

      if (user) {
        // Redirect based on user role
        switch (user.role) {
          case "owner":
            navigate("/dashboard");
            break;
          case "manager":
            navigate("/manager");
            break;
          case "kiosk":
            navigate(`/kiosk/${user.kioskLocationId}`);
            break;
          default:
            navigate("/");
        }
      }
    } catch (error: any) {
      setError(error.message || "Failed to sign in");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login">
      <div className="login__container">
        <div className="login__header">
          <Link to="/" className="login__logo">
            AdminBuddy
          </Link>
          <h1>Sign In</h1>
          <p>Access your AdminBuddy dashboard</p>
        </div>

        <form onSubmit={handleSubmit} className="login__form">
          {error && <div className="login__error">{error}</div>}

          <div className="login__field">
            <label htmlFor="email">Email</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              placeholder="Enter your email"
            />
          </div>

          <div className="login__field">
            <label htmlFor="password">Password</label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              placeholder="Enter your password"
            />
          </div>

          <Button
            type="submit"
            variant="primary"
            size="large"
            loading={loading}
            className="login__submit"
          >
            {loading ? "Signing In..." : "Sign In"}
          </Button>
        </form>

        <div className="login__footer">
          <p>
            Don't have an account?{" "}
            <Link to="/signup">Start your free trial</Link>
          </p>
          <p>
            <Link to="/">← Back to AdminBuddy</Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Login;
