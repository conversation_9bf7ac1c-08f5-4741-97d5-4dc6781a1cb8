import React from "react";
import { usePerformance } from "../../hooks";
import ErrorBoundary from "../../components/ui/ErrorBoundary";
import Button from "../../components/ui/Button";
import "./About.css";

const About: React.FC = () => {
  usePerformance({
    componentName: "About",
    threshold: 25,
  });

  return (
    <ErrorBoundary>
      <div className="about">
        <div className="about__hero">
          <div className="about__hero-content">
            <h1>About AdminBuddy</h1>
            <p className="about__intro">
              Built by small business owners, for small business owners.
              AdminBuddy solves real problems with simple, affordable task
              management designed for teams that actually work.
            </p>
          </div>
        </div>

        <div className="about__content">
          {/* Problem Section */}
          <section className="about__problem">
            <div className="about__problem-content">
              <div className="about__problem-text">
                <h2>The Problem We Solve</h2>
                <p>
                  Small businesses struggle with task management. Staff rely on
                  whiteboards, sticky notes, and memory to track daily routines.
                  Managers can't see what's been completed. Important tasks get
                  forgotten during shift changes.
                </p>
                <p>
                  Existing solutions are either too expensive (charging per
                  user) or too complex (built for large enterprises). Small
                  teams need something simple, affordable, and designed for
                  their reality.
                </p>
              </div>
              <div className="about__problem-image">
                <img
                  src="/images/about/messy-whiteboard.png"
                  alt="Cluttered whiteboard with sticky notes and unclear task lists"
                  className="about__image"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = "none";
                    const placeholder =
                      target.nextElementSibling as HTMLElement;
                    if (placeholder) placeholder.style.display = "flex";
                  }}
                />
                <div
                  className="about__image-placeholder"
                  style={{ display: "none" }}
                >
                  <span>Messy Task Management</span>
                  <small>Image: Cluttered whiteboard with sticky notes</small>
                </div>
              </div>
            </div>
          </section>

          {/* Solution Section */}
          <section className="about__solution">
            <div className="about__solution-content">
              <div className="about__solution-image">
                <img
                  src="/images/about/clean-tablet-interface.png"
                  alt="Clean tablet interface mounted in break room showing organized task list"
                  className="about__image"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = "none";
                    const placeholder =
                      target.nextElementSibling as HTMLElement;
                    if (placeholder) placeholder.style.display = "flex";
                  }}
                />
                <div
                  className="about__image-placeholder"
                  style={{ display: "none" }}
                >
                  <span>Clean Digital Solution</span>
                  <small>Image: Tablet mounted in break room</small>
                </div>
              </div>
              <div className="about__solution-text">
                <h2>Our Solution</h2>
                <p>
                  AdminBuddy provides a shared tablet interface that any staff
                  member can use to view and complete tasks. No individual
                  logins, no per-user fees, no complexity. Just organized task
                  management that works.
                </p>
                <p>
                  Business owners can set up recurring routines, managers can
                  track completion remotely, and staff can easily see what needs
                  to be done. It's task management designed for the way small
                  businesses actually work.
                </p>
              </div>
            </div>
          </section>

          <section className="about__section">
            <h2>Our Core Values</h2>
            <div className="about__philosophy">
              <div className="about__philosophy-item">
                <h3>🎯 Simplicity First</h3>
                <p>
                  We believe task management should be intuitive. If it takes
                  training to use, it's too complex. AdminBuddy works the way
                  your team already thinks.
                </p>
              </div>
              <div className="about__philosophy-item">
                <h3>💰 Fair Pricing</h3>
                <p>
                  Small businesses shouldn't pay per-user fees for shared tools.
                  Our pricing is based on locations, not headcount, making it
                  affordable as you grow.
                </p>
              </div>
              <div className="about__philosophy-item">
                <h3>🚀 Built for Reality</h3>
                <p>
                  We design for how small businesses actually work: shared
                  devices, shift-based teams, and managers who need remote
                  visibility without micromanaging.
                </p>
              </div>
              <div className="about__philosophy-item">
                <h3>🤝 Customer Success</h3>
                <p>
                  Your success is our success. We provide the support and
                  guidance you need to get organized and stay organized, without
                  the enterprise sales pressure.
                </p>
              </div>
            </div>
          </section>

          <section className="about__section">
            <h2>Why Small Businesses Choose AdminBuddy</h2>
            <div className="about__differences">
              <div className="about__difference-item">
                <div className="about__difference-icon">📱</div>
                <div className="about__difference-content">
                  <h3>Shared Tablet Design</h3>
                  <p>
                    One device, multiple users. No need for individual accounts
                    or per-user licensing fees.
                  </p>
                </div>
              </div>

              <div className="about__difference-item">
                <div className="about__difference-icon">🔄</div>
                <div className="about__difference-content">
                  <h3>Recurring Routines</h3>
                  <p>
                    Set up opening checklists, closing procedures, and
                    maintenance tasks that repeat automatically.
                  </p>
                </div>
              </div>

              <div className="about__difference-item">
                <div className="about__difference-icon">📊</div>
                <div className="about__difference-content">
                  <h3>Manager Visibility</h3>
                  <p>
                    Track task completion and get shift handoff notes from
                    anywhere, without micromanaging.
                  </p>
                </div>
              </div>

              <div className="about__difference-item">
                <div className="about__difference-icon">💰</div>
                <div className="about__difference-content">
                  <h3>Affordable Pricing</h3>
                  <p>
                    Pay per location, not per user. Start free and scale as your
                    business grows.
                  </p>
                </div>
              </div>

              <div className="about__difference-item">
                <div className="about__difference-icon">⚡</div>
                <div className="about__difference-content">
                  <h3>Quick Setup</h3>
                  <p>
                    Get organized in minutes, not weeks. No complex training or
                    lengthy onboarding required.
                  </p>
                </div>
              </div>

              <div className="about__difference-item">
                <div className="about__difference-icon">🏢</div>
                <div className="about__difference-content">
                  <h3>Multi-Location Support</h3>
                  <p>
                    Manage multiple business locations from a single dashboard
                    with location-specific tasks.
                  </p>
                </div>
              </div>
            </div>
          </section>

          {/* Founder Story Section */}
          <section className="about__founder">
            <div className="about__founder-content">
              <div className="about__founder-image">
                <img
                  src="/images/derek_macdonald.jpeg"
                  alt="Founder working with small business team"
                  className="about__founder-photo"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = "none";
                    const placeholder =
                      target.nextElementSibling as HTMLElement;
                    if (placeholder) placeholder.style.display = "flex";
                  }}
                />
                <div
                  className="about__founder-placeholder"
                  style={{ display: "none" }}
                >
                  <span>👨‍💼</span>
                  <small>Founder photo</small>
                </div>
              </div>
              <div className="about__founder-text">
                <h2>Built by Someone Who Gets It</h2>
                <p>
                  "I've managed teams in retail, food service, and small
                  offices. I've seen the frustration when tasks fall through the
                  cracks, the stress of not knowing if closing procedures were
                  followed, and the waste of paying per-user fees for shared
                  tools."
                </p>
                <p>
                  "AdminBuddy was born from real experience with these problems.
                  It's designed for the way small businesses actually work—not
                  how enterprise software thinks they should work."
                </p>
                <cite>— Derek MacDonald, Founder</cite>
              </div>
            </div>
          </section>

          {/* CTA Section */}
          <section className="about__cta">
            <div className="about__cta-content">
              <h2>Ready to Get Organized?</h2>
              <p>
                Join small businesses who are using AdminBuddy to streamline
                their operations and keep their teams on track.
              </p>
              <div className="about__cta-buttons">
                <Button variant="primary" size="large" href="/signup">
                  Start Free Trial
                </Button>
                <Button variant="secondary" size="large" href="/features">
                  See How It Works
                </Button>
              </div>
              <p className="about__cta-note">
                ✓ 14-day free trial • ✓ No credit card required
              </p>
            </div>
          </section>
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default About;
