import React from "react";
import Button from "../../components/ui/Button";
import { usePerformance } from "../../hooks";
import "./Home.css";

const Home: React.FC = () => {
  // Add performance monitoring
  usePerformance({
    componentName: "Home",
    threshold: 30,
  });

  return (
    <>
      {/* Hero Section */}
      <section className="home__hero">
        <div className="home__hero-content">
          <h1>Stop Losing Track of Daily Tasks</h1>
          <p className="home__hero-subtitle">
            AdminBuddy replaces whiteboards and sticky notes with a simple
            tablet system that keeps your team organized.
          </p>

          <div className="home__hero-cta">
            <Button variant="primary" size="large" href="/signup">
              Start Free Trial
            </Button>
          </div>

          <p className="home__hero-note">
            ✓ 14-day free trial • ✓ No credit card required
          </p>
        </div>
      </section>

      {/* Demo Section */}
      <section className="home__demo">
        <div className="home__container">
          <div className="home__demo-content">
            <div className="home__demo-text">
              <h2>See How Simple It Is</h2>
              <p>
                Staff walk up to a shared tablet, see what needs to be done, and
                tap to complete tasks. No logins, no training, no complexity.
              </p>
              <Button variant="secondary" href="/features">
                Explore All Features
              </Button>
            </div>
            <div className="home__demo-image">
              <img
                src="/images/screenshots/kiosk-interface.png"
                alt="AdminBuddy kiosk interface showing daily tasks"
                className="home__demo-screenshot"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = "none";
                  const placeholder = target.nextElementSibling as HTMLElement;
                  if (placeholder) placeholder.style.display = "flex";
                }}
              />
              <div
                className="home__demo-placeholder"
                style={{ display: "none" }}
              >
                <span>Kiosk Interface Demo</span>
                <small>Screenshot coming soon</small>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="home__stats">
        <div className="home__container">
          <div className="home__stats-content">
            <h2>Simple, Transparent Pricing</h2>
            <div className="home__stats-grid">
              <div className="home__stat">
                <span className="home__stat-number">$20</span>
                <span className="home__stat-label">Per Location/Month</span>
              </div>
              <div className="home__stat">
                <span className="home__stat-number">2 Week</span>
                <span className="home__stat-label">Free Trial</span>
              </div>
              <div className="home__stat">
                <span className="home__stat-number">No Limit</span>
                <span className="home__stat-label">Users Per Location</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Problem Section */}
      <section className="home__problem">
        <div className="home__container">
          <h2>Sound Familiar?</h2>
          <div className="home__problem-grid">
            <div className="home__problem-item">
              <span className="home__problem-icon">😤</span>
              <h3>"Did someone clean the restrooms?"</h3>
              <p>
                Important tasks get forgotten during shift changes. You're
                constantly asking what's been done and what still needs
                attention.
              </p>
            </div>
            <div className="home__problem-item">
              <span className="home__problem-icon">📋</span>
              <h3>"Where's that checklist again?"</h3>
              <p>
                Staff waste time hunting for procedures. Whiteboards get erased,
                printed lists disappear, and everyone does things differently.
              </p>
            </div>
            <div className="home__problem-item">
              <span className="home__problem-icon">💸</span>
              <h3>"$50 per user? Seriously?"</h3>
              <p>
                Enterprise tools charge per person for features you don't need.
                Why pay for individual accounts when you just need shared task
                lists?
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Solution Section */}
      <section className="home__solution">
        <div className="home__container">
          <h2>Finally, Task Management That Actually Works</h2>
          <div className="home__solution-grid">
            <div className="home__solution-item">
              <span className="home__solution-icon">📱</span>
              <h3>One Tablet, Everyone Uses It</h3>
              <p>
                Mount a tablet in your break room. Staff tap to see their tasks,
                check them off when done. No passwords, no per-user fees, no
                confusion.
              </p>
            </div>
            <div className="home__solution-item">
              <span className="home__solution-icon">🔄</span>
              <h3>Tasks That Repeat Themselves</h3>
              <p>
                Set up your opening checklist once, and it appears every
                morning. Closing procedures, weekly deep cleans—all automated.
              </p>
            </div>
            <div className="home__solution-item">
              <span className="home__solution-icon">📊</span>
              <h3>See Everything From Anywhere</h3>
              <p>
                Check completion status from your phone while at home. Get shift
                handoff notes. Know what's done without asking.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Social Proof Section */}
      <section className="home__social-proof">
        <div className="home__container">
          <h2>Join Hundreds of Small Businesses Getting Organized</h2>
          <div className="home__testimonials">
            <div className="home__testimonial">
              <p>
                "Finally, a task management system that doesn't require a PhD to
                use. Our staff love how simple it is."
              </p>
              <cite>— Sarah M., Coffee Shop Owner</cite>
            </div>
            <div className="home__testimonial">
              <p>
                "No more 'did someone do the closing checklist?' We can see
                everything from home and know our store is properly maintained."
              </p>
              <cite>— Mike R., Restaurant Manager</cite>
            </div>
            <div className="home__testimonial">
              <p>
                "Saved us $200/month compared to other solutions. Same
                functionality, fraction of the cost."
              </p>
              <cite>— Lisa K., Retail Store Owner</cite>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="home__cta">
        <div className="home__container">
          <h2>Ready to Get Organized?</h2>
          <p>
            Start your free trial today. No credit card required, no setup fees,
            no complicated onboarding. Just simple task management that works.
          </p>
          <div className="home__cta-buttons">
            <Button variant="primary" size="large" href="/signup">
              Start Free Trial
            </Button>
            <Button variant="secondary" size="large" href="/pricing">
              View Pricing
            </Button>
          </div>
          <p className="home__cta-note">
            ✓ 14-day free trial • ✓ 5-minute setup • ✓ Cancel anytime
          </p>
        </div>
      </section>
    </>
  );
};

export default Home;
