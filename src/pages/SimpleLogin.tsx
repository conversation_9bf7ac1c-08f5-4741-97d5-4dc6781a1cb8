import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTenant } from "../contexts/TenantContext";

const SimpleLogin: React.FC = () => {
  const navigate = useNavigate();
  const { login, signup, userProfile } = useTenant();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isSignupMode, setIsSignupMode] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email.trim() || !password.trim()) return;

    setLoading(true);
    setError("");

    // Check if this is a kiosk login (for extended sessions)
    const storedRedirectUrl = localStorage.getItem(
      "adminbuddy_redirect_after_login"
    );
    const isKioskLogin = storedRedirectUrl?.includes("/kiosk/");

    try {
      if (isSignupMode) {
        console.log("Creating new user account:", email);

        // Use the proper signup method from TenantContext (Firebase Auth REST API)
        await signup(email.trim(), password);
        console.log("✅ User created and logged in successfully");

        // Check for stored redirect URL first
        const storedRedirectUrl = localStorage.getItem(
          "adminbuddy_redirect_after_login"
        );

        if (storedRedirectUrl) {
          console.log(
            "🔄 New user - redirecting to stored URL:",
            storedRedirectUrl
          );
          localStorage.removeItem("adminbuddy_redirect_after_login");
          navigate(storedRedirectUrl);
        } else {
          console.log("🎉 New user - redirecting to onboarding");
          navigate("/onboarding");
        }
      } else {
        console.log("🔐 Pure REST login:", email);
        await login(email.trim(), password, isKioskLogin); // Pass rememberMe for kiosk logins
        console.log("✅ Login successful, navigating based on role");

        // Check for stored redirect URL first
        const storedRedirectUrl = localStorage.getItem(
          "adminbuddy_redirect_after_login"
        );

        console.log("🔍 Checking for stored redirect URL:", storedRedirectUrl);

        if (storedRedirectUrl) {
          console.log("🔄 Redirecting to stored URL:", storedRedirectUrl);
          localStorage.removeItem("adminbuddy_redirect_after_login");
          console.log("🧹 Removed stored redirect URL from localStorage");
          navigate(storedRedirectUrl);
          return; // Important: return early to prevent default navigation
        } else {
          console.log(
            "📍 No stored redirect URL found, using default navigation"
          );

          // Navigation based on user profile (TenantContext handles auth state)
          if (userProfile) {
            switch (userProfile.role) {
              case "owner":
                navigate("/dashboard");
                break;
              case "manager":
                navigate("/manager");
                break;
              case "employee":
                navigate("/employee");
                break;
              default:
                navigate("/dashboard");
            }
          } else {
            // Default navigation if profile not immediately available
            navigate("/dashboard");
          }
        }
      }
    } catch (error: any) {
      console.error("Authentication error:", error);
      setError(
        error.message ||
          `Failed to ${
            isSignupMode ? "create account" : "login"
          }. Please try again.`
      );
    } finally {
      setLoading(false);
    }
  };

  // No predefined tenants - each user gets their own

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "#f5f5f5",
      }}
    >
      <div
        style={{
          backgroundColor: "white",
          padding: "40px",
          borderRadius: "8px",
          boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
          width: "100%",
          maxWidth: "400px",
        }}
      >
        <div style={{ textAlign: "center", marginBottom: "30px" }}>
          <h1 style={{ color: "#2c3e50", marginBottom: "10px" }}>AdminBuddy</h1>
          <p style={{ color: "#666", margin: 0 }}>
            Simple Business Task Management
          </p>
        </div>

        <div style={{ textAlign: "center", marginBottom: "20px" }}>
          <button
            type="button"
            onClick={() => setIsSignupMode(false)}
            style={{
              padding: "8px 16px",
              marginRight: "10px",
              backgroundColor: !isSignupMode ? "#3498db" : "#ecf0f1",
              color: !isSignupMode ? "white" : "#2c3e50",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
            }}
          >
            Login
          </button>
          <button
            type="button"
            onClick={() => setIsSignupMode(true)}
            style={{
              padding: "8px 16px",
              backgroundColor: isSignupMode ? "#3498db" : "#ecf0f1",
              color: isSignupMode ? "white" : "#2c3e50",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
            }}
          >
            Sign Up
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div style={{ marginBottom: "20px" }}>
            <label
              style={{
                display: "block",
                marginBottom: "5px",
                fontWeight: "bold",
                color: "#333",
              }}
            >
              Email:
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email address"
              required
              disabled={loading}
              style={{
                width: "100%",
                padding: "12px",
                border: "1px solid #ddd",
                borderRadius: "4px",
                fontSize: "16px",
                boxSizing: "border-box",
                opacity: loading ? 0.6 : 1,
              }}
            />
          </div>

          <div style={{ marginBottom: "20px" }}>
            <label
              style={{
                display: "block",
                marginBottom: "5px",
                fontWeight: "bold",
                color: "#333",
              }}
            >
              Password:
            </label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter your password"
              required
              disabled={loading}
              style={{
                width: "100%",
                padding: "12px",
                border: "1px solid #ddd",
                borderRadius: "4px",
                fontSize: "16px",
                boxSizing: "border-box",
                opacity: loading ? 0.6 : 1,
              }}
            />
          </div>

          {error && (
            <div
              style={{
                marginBottom: "20px",
                padding: "10px",
                backgroundColor: "#fee",
                border: "1px solid #fcc",
                borderRadius: "4px",
                color: "#c33",
                fontSize: "14px",
              }}
            >
              {error}
            </div>
          )}

          <button
            type="submit"
            disabled={loading}
            style={{
              width: "100%",
              padding: "12px",
              backgroundColor: loading ? "#bbb" : "#3498db",
              color: "white",
              border: "none",
              borderRadius: "4px",
              fontSize: "16px",
              fontWeight: "bold",
              cursor: loading ? "not-allowed" : "pointer",
            }}
          >
            {loading
              ? isSignupMode
                ? "🔄 Creating Account..."
                : "🔄 Logging in..."
              : isSignupMode
              ? "📝 Create Account"
              : "🔐 Login to AdminBuddy"}
          </button>
        </form>

        <div
          style={{
            marginTop: "20px",
            padding: "15px",
            backgroundColor: "#e8f4fd",
            borderRadius: "4px",
            fontSize: "14px",
            color: "#2c3e50",
          }}
        >
          <strong>Secure Authentication:</strong>
          <br />
          • Password-protected accounts
          <br />
          • Each user gets their own private business data
          <br />• Full access to all AdminBuddy features
        </div>
      </div>
    </div>
  );
};

export default SimpleLogin;
