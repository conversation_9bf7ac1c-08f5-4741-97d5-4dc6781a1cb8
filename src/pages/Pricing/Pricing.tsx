import React from "react";
import Button from "../../components/ui/Button";
import "./Pricing.css";

const Pricing: React.FC = () => {
  return (
    <div className="pricing">
      <section className="pricing__hero">
        <div className="pricing__hero-content">
          <h1>Pricing Made Simple</h1>
          <p>
            Pay per location, not per user. Start with a 2-week free trial and
            scale as you grow with no hidden fees or per-user charges.
          </p>
        </div>
      </section>

      <section className="pricing__plans">
        <div className="pricing__container">
          <div className="pricing__plan pricing__plan--featured">
            <div className="pricing__badge">Most Popular</div>
            <h3>Monthly</h3>
            <div className="pricing__price">
              <span className="pricing__amount">$20</span>
              <span className="pricing__period">/location/month</span>
            </div>
            <ul className="pricing__features">
              <li>2-week free trial</li>
              <li>Unlimited tasks & users</li>
              <li>Manager dashboard</li>
              <li>Shift handoffs</li>
              <li>Task completion tracking</li>
              <li>Email support</li>
              <li>Cancel anytime</li>
            </ul>
            <Button variant="primary" size="large" href="/signup">
              Start Free Trial
            </Button>
            <p className="pricing__trial-note">
              14 days free, then $20/location/month
            </p>
          </div>

          <div className="pricing__plan">
            <div className="pricing__savings">1 month free</div>
            <h3>Yearly</h3>
            <div className="pricing__price">
              <span className="pricing__amount">$220</span>
              <span className="pricing__period">/location/year</span>
            </div>
            <div className="pricing__yearly-note">
              Equivalent to $18.33/month (1 month free)
            </div>
            <ul className="pricing__features">
              <li>2-week free trial</li>
              <li>Everything in Monthly</li>
              <li>1 month free per year</li>
              <li>Priority support</li>
              <li>Advanced reporting</li>
              <li>Custom task templates</li>
            </ul>
            <Button variant="secondary" size="large" href="/signup">
              Start Free Trial
            </Button>
            <p className="pricing__trial-note">
              14 days free, then $220/location/year
            </p>
          </div>
        </div>

        <div className="pricing__enterprise">
          <div className="pricing__enterprise-content">
            <h3>Need more than 10 locations?</h3>
            <p>
              Contact us for volume discounts and custom enterprise features
              like dedicated support, training, and custom integrations.
            </p>
            <Button variant="secondary" href="/contact">
              Contact Sales
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Pricing;
