import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  getAllLocations,
  getAllRoutines,
  getAllRoutineTasks,
  getAllLocationRoutineSchedules,
  getTenantRoles,
} from "../../services/adminBuddyFirebaseService";
import Button from "../ui/Button";
import "./SetupWizard.css";

interface SetupStep {
  id: string;
  title: string;
  description: string;
  icon: string;
  completed: boolean;
  required: boolean;
  path: string;
}

interface SetupWizardProps {
  onClose: () => void;
  onComplete: () => void;
  tenantId?: string;
  userEmail?: string;
  onRefresh?: () => void;
}

const SetupWizard: React.FC<SetupWizardProps> = ({
  onClose,
  onComplete,
  tenantId,
  userEmail,
  onRefresh,
}) => {
  const navigate = useNavigate();
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const [setupSteps, setSetupSteps] = useState<SetupStep[]>([
    {
      id: "locations",
      title: "Add Your First Location",
      description: "Set up your business locations where tasks will be managed",
      icon: "🏢",
      completed: false,
      required: true,
      path: "/dashboard/locations",
    },
    {
      id: "roles",
      title: "Create Employee Roles",
      description:
        "Define roles like Manager, Cashier, etc. for task assignment",
      icon: "👥",
      completed: false,
      required: false,
      path: "/dashboard/roles",
    },
    {
      id: "routines",
      title: "Design Task Routines",
      description:
        "Create routine templates like 'Opening Store' or 'Closing Store'",
      icon: "🔄",
      completed: false,
      required: true,
      path: "/dashboard/routines",
    },
    {
      id: "routine-tasks",
      title: "Add Tasks to Routines",
      description: "Define specific tasks within each routine with priorities",
      icon: "📋",
      completed: false,
      required: true,
      path: "/dashboard/routine-tasks",
    },
    {
      id: "schedules",
      title: "Schedule Your Routines",
      description:
        "Set when routines run at each location (daily, weekly, etc.)",
      icon: "📅",
      completed: false,
      required: true,
      path: "/dashboard/schedules",
    },
    {
      id: "kiosk",
      title: "Test Your Kiosk",
      description: "Try out the tablet interface your team will use",
      icon: "📱",
      completed: false,
      required: false,
      path: "/kiosk",
    },
  ]);

  // Function to check setup completion (extracted for reuse)
  const checkStepCompletion = async () => {
    if (!tenantId) return;

    try {
      setIsRefreshing(true);
      console.log("🔍 Checking setup completion status...");

      // Fetch all data to check completion
      const [locations, routines, routineTasks, schedules, roles] =
        await Promise.all([
          getAllLocations(tenantId),
          getAllRoutines(tenantId),
          getAllRoutineTasks(tenantId),
          getAllLocationRoutineSchedules(tenantId),
          getTenantRoles(tenantId),
        ]);

      // Update completion status based on actual data
      setSetupSteps((steps) =>
        steps.map((step) => {
          let completed = false;

          switch (step.id) {
            case "locations":
              completed = locations.length > 0;
              break;
            case "roles":
              // Only count custom roles, not default tenant roles
              const customRoles = roles.filter(
                (role) =>
                  ![
                    "Any Employee",
                    "Keyholder",
                    "Manager",
                    "Employee",
                    "Admin",
                  ].includes(role.name)
              );
              completed = customRoles.length > 0;
              break;
            case "routines":
              completed = routines.length > 0;
              break;
            case "routine-tasks":
              completed = routineTasks.length > 0;
              break;
            case "schedules":
              completed = schedules.length > 0;
              break;
            case "kiosk":
              // Consider kiosk "completed" if they have a complete setup ready for use
              completed =
                locations.length > 0 &&
                routines.length > 0 &&
                routineTasks.length > 0 &&
                schedules.length > 0;
              break;
            default:
              completed = step.completed;
          }

          return { ...step, completed };
        })
      );

      console.log(
        `✅ Setup status: ${locations.length} locations, ${routines.length} routines, ${routineTasks.length} tasks, ${schedules.length} schedules, ${roles.length} roles`
      );
    } catch (error) {
      console.error("❌ Error checking setup completion:", error);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Check completion status on initial load
  useEffect(() => {
    checkStepCompletion();
  }, [tenantId]);

  // Add window focus listener to refresh progress when user returns
  useEffect(() => {
    const handleWindowFocus = () => {
      console.log("🔄 Window focused - refreshing setup progress...");
      checkStepCompletion();
    };

    window.addEventListener("focus", handleWindowFocus);

    return () => {
      window.removeEventListener("focus", handleWindowFocus);
    };
  }, [tenantId]);

  // Add periodic refresh to check for updates every 5 seconds
  useEffect(() => {
    if (!tenantId) return;

    const interval = setInterval(() => {
      console.log("⏰ Periodic refresh - checking setup progress...");
      checkStepCompletion();
    }, 5000); // Check every 5 seconds

    return () => {
      clearInterval(interval);
    };
  }, [tenantId]);

  const currentStep = setupSteps[currentStepIndex];
  const completedSteps = setupSteps.filter((step) => step.completed).length;
  const totalSteps = setupSteps.length;
  const progressPercentage = (completedSteps / totalSteps) * 100;

  const handleNextStep = () => {
    if (currentStepIndex < setupSteps.length - 1) {
      setCurrentStepIndex(currentStepIndex + 1);
    } else {
      onComplete();
    }
  };

  const handlePreviousStep = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(currentStepIndex - 1);
    }
  };

  const handleGoToStep = (path: string) => {
    navigate(path);
    onClose();
    // Refresh setup progress after navigation
    if (onRefresh) {
      setTimeout(onRefresh, 1000); // Give time for navigation to complete
    }
  };

  // Function to manually refresh setup progress
  const refreshSetupProgress = () => {
    console.log("🔄 Manual refresh requested...");
    checkStepCompletion();
  };

  const handleSkipSetup = () => {
    onComplete();
  };

  return (
    <div className="setup-wizard">
      <div className="setup-wizard__overlay" onClick={onClose} />
      <div className="setup-wizard__container">
        <div className="setup-wizard__header">
          <div className="setup-wizard__title">
            <h2>🚀 Welcome to AdminBuddy!</h2>
            <p>
              Let's get your task management system set up in just a few steps
            </p>
          </div>
          <button className="setup-wizard__close" onClick={onClose}>
            ×
          </button>
        </div>

        <div className="setup-wizard__progress">
          <div className="progress-bar">
            <div
              className="progress-fill"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
          <div className="progress-info">
            <span className="progress-text">
              {completedSteps} of {totalSteps} steps completed
            </span>
            <button
              className={`refresh-button ${isRefreshing ? "refreshing" : ""}`}
              onClick={refreshSetupProgress}
              title="Refresh progress"
              disabled={isRefreshing}
            >
              {isRefreshing ? "⏳" : "🔄"}
            </button>
          </div>
        </div>

        <div className="setup-wizard__content">
          <div className="setup-wizard__steps">
            {setupSteps.map((step, index) => (
              <div
                key={step.id}
                className={`setup-step ${
                  index === currentStepIndex ? "setup-step--current" : ""
                } ${step.completed ? "setup-step--completed" : ""}`}
              >
                <div className="setup-step__icon">
                  {step.completed ? "✅" : step.icon}
                </div>
                <div className="setup-step__content">
                  <h3>{step.title}</h3>
                  <p>{step.description}</p>
                  {step.required && (
                    <span className="setup-step__required">Required</span>
                  )}
                </div>
                <div className="setup-step__actions">
                  {step.completed ? (
                    <span className="completed-badge">✓ Complete</span>
                  ) : (
                    <Button
                      variant="primary"
                      size="small"
                      onClick={() => handleGoToStep(step.path)}
                    >
                      Set Up Now
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>

          <div className="setup-wizard__current-step">
            <div className="current-step__header">
              <span className="current-step__icon">{currentStep.icon}</span>
              <div>
                <h3>{currentStep.title}</h3>
                <p>{currentStep.description}</p>
              </div>
            </div>

            <div className="current-step__tips">
              <h4>💡 Quick Tips:</h4>
              {currentStep.id === "locations" && (
                <ul>
                  <li>Start with just one location to keep things simple</li>
                  <li>You can add more locations later as you expand</li>
                  <li>Include the full address for easy identification</li>
                </ul>
              )}
              {currentStep.id === "roles" && (
                <ul>
                  <li>Common roles: Manager, Shift Lead, Cashier, Cleaner</li>
                  <li>Roles help organize who does which tasks</li>
                  <li>You can always add more roles later</li>
                </ul>
              )}
              {currentStep.id === "routines" && (
                <ul>
                  <li>
                    Think about recurring activities: Opening, Closing, Weekly
                    Clean
                  </li>
                  <li>Start with 2-3 basic routines</li>
                  <li>Each routine will contain multiple tasks</li>
                </ul>
              )}
              {currentStep.id === "routine-tasks" && (
                <ul>
                  <li>Break down routines into specific, actionable tasks</li>
                  <li>Set priorities: High, Medium, Low</li>
                  <li>Assign tasks to specific roles when needed</li>
                </ul>
              )}
              {currentStep.id === "schedules" && (
                <ul>
                  <li>
                    Set when each routine should run (daily, weekly, etc.)
                  </li>
                  <li>Choose specific times for time-sensitive routines</li>
                  <li>Different locations can have different schedules</li>
                </ul>
              )}
              {currentStep.id === "kiosk" && (
                <ul>
                  <li>This is what your team will see on their tablet</li>
                  <li>Tasks are organized by routine and priority</li>
                  <li>Staff can tap to complete tasks and add notes</li>
                </ul>
              )}
            </div>
          </div>
        </div>

        <div className="setup-wizard__actions">
          <div className="setup-wizard__navigation">
            <Button variant="secondary" onClick={handleSkipSetup}>
              Skip Setup
            </Button>

            <div className="setup-wizard__nav-buttons">
              <Button
                variant="secondary"
                onClick={handlePreviousStep}
                disabled={currentStepIndex === 0}
              >
                Previous
              </Button>
              <Button variant="primary" onClick={handleNextStep}>
                {currentStepIndex === setupSteps.length - 1 ? "Finish" : "Next"}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SetupWizard;
