import React, { useState } from "react";
import { NavLink, Link } from "react-router-dom";
import { useViewport } from "../../../hooks";
import Button from "../../ui/Button";
import "./Header.css";

const Header: React.FC = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { isMobile } = useViewport();

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  const navigationItems = [
    { to: "/features", label: "Features" },
    { to: "/pricing", label: "Pricing" },
    { to: "/about", label: "About" },
    { to: "/contact", label: "Contact" },
  ];

  return (
    <header className="header">
      <Link to="/" className="header__brand-link">
        <div className="header__brand-container">
          <div className="header__brand-name">AdminBuddy</div>
          <div className="header__tagline hidden-mobile">
            Streamline Your Team's Daily Tasks
          </div>
        </div>
      </Link>

      {isMobile ? (
        <>
          <button
            className="header__mobile-toggle"
            onClick={toggleMobileMenu}
            aria-label="Toggle navigation menu"
            aria-expanded={isMobileMenuOpen}
          >
            <span
              className={`header__hamburger ${isMobileMenuOpen ? "open" : ""}`}
            >
              <span></span>
              <span></span>
              <span></span>
            </span>
          </button>

          <nav
            className={`header__nav header__nav--mobile ${
              isMobileMenuOpen ? "open" : ""
            }`}
          >
            {navigationItems.map(({ to, label }) => (
              <NavLink
                key={to}
                to={to}
                className={({ isActive }) =>
                  isActive
                    ? "header__nav-link header__nav-link--active"
                    : "header__nav-link"
                }
                onClick={closeMobileMenu}
              >
                {label}
              </NavLink>
            ))}
            <div className="header__mobile-cta">
              <Button variant="secondary" size="small" href="/login">
                Sign In
              </Button>
              <Button variant="primary" size="small" href="/signup">
                Start Free Trial
              </Button>
            </div>
          </nav>

          {isMobileMenuOpen && (
            <div
              className="header__overlay"
              onClick={closeMobileMenu}
              aria-hidden="true"
            />
          )}
        </>
      ) : (
        <div className="header__nav-container">
          <nav className="header__nav">
            {navigationItems.map(({ to, label }) => (
              <NavLink
                key={to}
                to={to}
                className={({ isActive }) =>
                  isActive
                    ? "header__nav-link header__nav-link--active"
                    : "header__nav-link"
                }
              >
                {label}
              </NavLink>
            ))}
          </nav>
          <div className="header__cta">
            <Button variant="secondary" size="small" href="/login">
              Sign In
            </Button>
            <Button variant="primary" size="small" href="/signup">
              Start Free Trial
            </Button>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
