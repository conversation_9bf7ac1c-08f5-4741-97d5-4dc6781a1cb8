import React from "react";
import { useNavigate } from "react-router-dom";
import { useTenant } from "../../contexts/TenantContext";
import { getTrialStatus, getTrialMessage, getTrialWarningLevel } from "../../services/trialService";
import "./TrialBanner.css";

interface TrialBannerProps {
  className?: string;
}

const TrialBanner: React.FC<TrialBannerProps> = ({ className = "" }) => {
  const { userProfile } = useTenant();
  const navigate = useNavigate();

  const trialStatus = getTrialStatus(userProfile);
  const message = getTrialMessage(userProfile);
  const warningLevel = getTrialWarningLevel(userProfile);

  // Don't show banner if user has active subscription
  if (trialStatus.hasActiveSubscription || warningLevel === "none") {
    return null;
  }

  const handleSubscribeClick = () => {
    navigate("/billing");
  };

  const getBannerClass = () => {
    switch (warningLevel) {
      case "error":
        return "trial-banner trial-banner--error";
      case "warning":
        return "trial-banner trial-banner--warning";
      case "info":
        return "trial-banner trial-banner--info";
      default:
        return "trial-banner";
    }
  };

  const getIcon = () => {
    switch (warningLevel) {
      case "error":
        return "⚠️";
      case "warning":
        return "⏰";
      case "info":
        return "ℹ️";
      default:
        return "ℹ️";
    }
  };

  return (
    <div className={`${getBannerClass()} ${className}`}>
      <div className="trial-banner__content">
        <div className="trial-banner__message">
          <span className="trial-banner__icon">{getIcon()}</span>
          <span className="trial-banner__text">{message}</span>
        </div>
        <button
          className="trial-banner__button"
          onClick={handleSubscribeClick}
        >
          {trialStatus.isExpired ? "Subscribe Now" : "Upgrade Now"}
        </button>
      </div>
    </div>
  );
};

export default TrialBanner;
