.setup-progress {
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  color: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 12px rgba(10, 45, 105, 0.15);
  transition: all 0.3s ease;
}

.setup-progress--collapsed {
  padding: 1rem 1.5rem;
}

.setup-progress--complete {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.setup-progress--compact {
  background: white;
  border: 2px solid #0a2d69;
  color: #0a2d69;
  padding: 1rem;
  margin-bottom: 1rem;
}

.setup-progress__header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.setup-progress--collapsed .setup-progress__header {
  margin-bottom: 0;
}

.setup-progress--compact .setup-progress__header {
  margin-bottom: 0;
  align-items: center;
}

.setup-progress__title {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  flex: 1;
}

.setup-progress__icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.setup-progress__title h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.setup-progress__title p {
  margin: 0;
  opacity: 0.9;
  font-size: 0.9rem;
  line-height: 1.4;
}

.setup-progress--compact .setup-progress__title h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.setup-progress__controls {
  display: flex;
  gap: 0.5rem;
  align-items: flex-start;
}

.setup-progress__toggle,
.setup-progress__dismiss {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.setup-progress--compact .setup-progress__toggle,
.setup-progress--compact .setup-progress__dismiss {
  background: rgba(10, 45, 105, 0.1);
  color: #0a2d69;
}

.setup-progress__toggle:hover,
.setup-progress__dismiss:hover {
  background: rgba(255, 255, 255, 0.3);
}

.setup-progress--compact .setup-progress__toggle:hover,
.setup-progress--compact .setup-progress__dismiss:hover {
  background: rgba(10, 45, 105, 0.2);
}

.setup-progress__summary {
  margin-bottom: 1.5rem;
}

.setup-progress__bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.setup-progress--compact .setup-progress__bar {
  background: rgba(10, 45, 105, 0.1);
  margin-bottom: 0.25rem;
}

.setup-progress__fill {
  height: 100%;
  background: white;
  transition: width 0.3s ease;
  border-radius: 4px;
}

.setup-progress--compact .setup-progress__fill {
  background: #0a2d69;
}

.setup-progress__text {
  font-size: 0.9rem;
  opacity: 0.9;
  font-weight: 500;
}

.setup-progress--compact .setup-progress__text {
  font-size: 0.8rem;
  color: #666;
}

.setup-progress__next-task {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

.next-task__header {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.next-task__icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.next-task__header h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.next-task__header p {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.9;
}

.setup-progress__high-priority {
  margin-bottom: 1.5rem;
}

.setup-progress__high-priority h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.setup-progress__tasks {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.setup-task {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.setup-task:hover {
  background: rgba(255, 255, 255, 0.15);
}

.setup-task__icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.setup-task__content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.setup-task__title {
  font-weight: 500;
  font-size: 0.9rem;
}

.setup-task__description {
  font-size: 0.8rem;
  opacity: 0.8;
}

.setup-task__arrow {
  font-size: 0.9rem;
  opacity: 0.7;
  flex-shrink: 0;
}

.setup-progress__actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.setup-progress--complete .setup-progress__actions {
  justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .setup-progress {
    padding: 1rem;
  }

  .setup-progress__header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .setup-progress__controls {
    align-self: flex-end;
  }

  .setup-progress__next-task {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .setup-progress__actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .setup-progress--compact .setup-progress__header {
    flex-direction: row;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .setup-progress__title {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }

  .setup-progress__icon {
    align-self: center;
  }

  .next-task__header {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .setup-task {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .setup-task__content {
    align-items: center;
  }
}
