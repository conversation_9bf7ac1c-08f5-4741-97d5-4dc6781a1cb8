import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  getAllLocations,
  getAllRoutines,
  getAllRoutineTasks,
  getAllLocationRoutineSchedules,
  getTenantRoles,
} from "../../services/adminBuddyFirebaseService";
import Button from "../ui/Button";
import "./SetupProgress.css";

interface SetupTask {
  id: string;
  title: string;
  description: string;
  icon: string;
  completed: boolean;
  path: string;
  priority: "high" | "medium" | "low";
}

interface SetupProgressProps {
  onOpenWizard: () => void;
  onDismiss: () => void;
  compact?: boolean;
  tenantId?: string;
}

const SetupProgress: React.FC<SetupProgressProps> = ({
  onOpenWizard,
  onDismiss,
  compact = false,
  tenantId,
}) => {
  const navigate = useNavigate();
  const [setupTasks, setSetupTasks] = useState<SetupTask[]>([
    {
      id: "locations",
      title: "Add Your First Location",
      description: "Set up where your business operates",
      icon: "🏢",
      completed: false,
      path: "/dashboard/locations",
      priority: "high",
    },
    {
      id: "routines",
      title: "Create Task Routines",
      description: "Build templates for recurring work",
      icon: "🔄",
      completed: false,
      path: "/dashboard/routines",
      priority: "high",
    },
    {
      id: "routine-tasks",
      title: "Add Tasks to Routines",
      description: "Define specific tasks within routines",
      icon: "📋",
      completed: false,
      path: "/dashboard/routine-tasks",
      priority: "high",
    },
    {
      id: "schedules",
      title: "Schedule Your Routines",
      description: "Set when routines should run",
      icon: "📅",
      completed: false,
      path: "/dashboard/schedules",
      priority: "medium",
    },
    {
      id: "roles",
      title: "Create Employee Roles",
      description: "Organize task assignments by role",
      icon: "👥",
      completed: false,
      path: "/dashboard/roles",
      priority: "low",
    },
    {
      id: "kiosk",
      title: "Test Your Kiosk",
      description: "Try the tablet interface",
      icon: "📱",
      completed: false,
      path: "/kiosk",
      priority: "low",
    },
  ]);

  const [isCollapsed, setIsCollapsed] = useState(false);

  // Check completion status based on actual data
  useEffect(() => {
    const checkSetupCompletion = async () => {
      if (!tenantId) return;

      try {
        console.log("🔍 Checking setup completion...");

        // Fetch all data to check completion
        const [locations, routines, routineTasks, schedules, roles] =
          await Promise.all([
            getAllLocations(tenantId),
            getAllRoutines(tenantId),
            getAllRoutineTasks(tenantId),
            getAllLocationRoutineSchedules(tenantId),
            getTenantRoles(tenantId),
          ]);

        // Update completion status based on actual data
        setSetupTasks((tasks) =>
          tasks.map((task) => {
            let completed = false;

            switch (task.id) {
              case "locations":
                completed = locations.length > 0;
                break;
              case "roles":
                // Only count custom roles, not default tenant roles
                const customRoles = roles.filter(
                  (role) =>
                    ![
                      "Any Employee",
                      "Keyholder",
                      "Manager",
                      "Employee",
                      "Admin",
                    ].includes(role.name)
                );
                completed = customRoles.length > 0;
                break;
              case "routines":
                completed = routines.length > 0;
                break;
              case "routine-tasks":
                completed = routineTasks.length > 0;
                break;
              case "schedules":
                completed = schedules.length > 0;
                break;
              case "kiosk":
                // Consider kiosk "completed" if they have a complete setup ready for use
                completed =
                  locations.length > 0 &&
                  routines.length > 0 &&
                  routineTasks.length > 0 &&
                  schedules.length > 0;
                break;
              default:
                completed = task.completed;
            }

            return { ...task, completed };
          })
        );

        console.log(
          `✅ Setup status: ${locations.length} locations, ${routines.length} routines, ${routineTasks.length} tasks, ${schedules.length} schedules, ${roles.length} roles`
        );
      } catch (error) {
        console.error("❌ Error checking setup completion:", error);
      }
    };

    checkSetupCompletion();
  }, [tenantId]);

  const completedTasks = setupTasks.filter((task) => task.completed);
  const totalTasks = setupTasks.length;
  const progressPercentage = (completedTasks.length / totalTasks) * 100;
  const isSetupComplete = completedTasks.length === totalTasks;

  const highPriorityTasks = setupTasks.filter(
    (task) => !task.completed && task.priority === "high"
  );
  const nextTask = setupTasks.find((task) => !task.completed);

  const handleTaskClick = (path: string) => {
    navigate(path);
  };

  if (isSetupComplete) {
    return (
      <div className="setup-progress setup-progress--complete">
        <div className="setup-progress__header">
          <div className="setup-progress__icon">🎉</div>
          <div>
            <h3>Setup Complete!</h3>
            <p>Your AdminBuddy workspace is ready to go</p>
          </div>
          <button
            className="setup-progress__dismiss"
            onClick={onDismiss}
            title="Dismiss"
          >
            ×
          </button>
        </div>
        <div className="setup-progress__actions">
          <Button
            variant="primary"
            size="small"
            onClick={() => navigate("/kiosk")}
          >
            Open Kiosk Interface
          </Button>
        </div>
      </div>
    );
  }

  if (compact) {
    return (
      <div className="setup-progress setup-progress--compact">
        <div className="setup-progress__header">
          <div className="setup-progress__icon">🚀</div>
          <div>
            <h4>Setup Progress</h4>
            <div className="setup-progress__bar">
              <div
                className="setup-progress__fill"
                style={{ width: `${progressPercentage}%` }}
              />
            </div>
            <span className="setup-progress__text">
              {completedTasks.length} of {totalTasks} completed
            </span>
          </div>
          <Button variant="primary" size="small" onClick={onOpenWizard}>
            Continue Setup
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`setup-progress ${
        isCollapsed ? "setup-progress--collapsed" : ""
      }`}
    >
      <div className="setup-progress__header">
        <div className="setup-progress__title">
          <div className="setup-progress__icon">🚀</div>
          <div>
            <h3>Complete Your Setup</h3>
            <p>Get the most out of AdminBuddy by completing these steps</p>
          </div>
        </div>
        <div className="setup-progress__controls">
          <button
            className="setup-progress__toggle"
            onClick={() => setIsCollapsed(!isCollapsed)}
            title={isCollapsed ? "Expand" : "Collapse"}
          >
            {isCollapsed ? "▼" : "▲"}
          </button>
          <button
            className="setup-progress__dismiss"
            onClick={onDismiss}
            title="Dismiss"
          >
            ×
          </button>
        </div>
      </div>

      {!isCollapsed && (
        <>
          <div className="setup-progress__summary">
            <div className="setup-progress__bar">
              <div
                className="setup-progress__fill"
                style={{ width: `${progressPercentage}%` }}
              />
            </div>
            <span className="setup-progress__text">
              {completedTasks.length} of {totalTasks} steps completed
            </span>
          </div>

          {nextTask && (
            <div className="setup-progress__next-task">
              <div className="next-task__header">
                <span className="next-task__icon">{nextTask.icon}</span>
                <div>
                  <h4>Next: {nextTask.title}</h4>
                  <p>{nextTask.description}</p>
                </div>
              </div>
              <Button
                variant="primary"
                size="small"
                onClick={() => handleTaskClick(nextTask.path)}
              >
                Start Now
              </Button>
            </div>
          )}

          {highPriorityTasks.length > 0 && (
            <div className="setup-progress__high-priority">
              <h4>🔥 High Priority Tasks</h4>
              <div className="setup-progress__tasks">
                {highPriorityTasks.slice(0, 3).map((task) => (
                  <div
                    key={task.id}
                    className="setup-task"
                    onClick={() => handleTaskClick(task.path)}
                  >
                    <span className="setup-task__icon">{task.icon}</span>
                    <div className="setup-task__content">
                      <span className="setup-task__title">{task.title}</span>
                      <span className="setup-task__description">
                        {task.description}
                      </span>
                    </div>
                    <span className="setup-task__arrow">→</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="setup-progress__actions">
            <Button variant="secondary" size="small" onClick={onOpenWizard}>
              Open Setup Wizard
            </Button>
            <Button
              variant="primary"
              size="small"
              onClick={() => nextTask && handleTaskClick(nextTask.path)}
              disabled={!nextTask}
            >
              Continue Setup
            </Button>
          </div>
        </>
      )}
    </div>
  );
};

export default SetupProgress;
