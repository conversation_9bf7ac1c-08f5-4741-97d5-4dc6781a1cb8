.instructor-login {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  padding: 2rem;
}

.instructor-login__container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  padding: 3rem;
  width: 100%;
  max-width: 420px;
}

.instructor-login__header {
  text-align: center;
  margin-bottom: 2rem;
}

.instructor-login__header h1 {
  margin: 0 0 0.5rem 0;
  color: #0a2d69;
  font-size: 2rem;
  font-weight: 700;
}

.instructor-login__header p {
  margin: 0;
  color: #6b7280;
  font-size: 1rem;
  line-height: 1.5;
}

.instructor-login__form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.instructor-login__error {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  text-align: center;
}

.instructor-login__field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.instructor-login__field label {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.instructor-login__field input {
  padding: 0.875rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.instructor-login__field input:focus {
  outline: none;
  border-color: #0a2d69;
  box-shadow: 0 0 0 3px rgba(10, 45, 105, 0.1);
}

.instructor-login__field input:disabled {
  background: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

.instructor-login__submit {
  margin-top: 1rem;
  padding: 1rem;
  font-size: 1rem;
  font-weight: 600;
}

.instructor-login__footer {
  margin-top: 2rem;
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

.instructor-login__footer p {
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.instructor-login__footer a {
  color: #0a2d69;
  text-decoration: none;
  font-weight: 500;
}

.instructor-login__footer a:hover {
  text-decoration: underline;
}

/* Responsive design */
@media (max-width: 480px) {
  .instructor-login {
    padding: 1rem;
  }
  
  .instructor-login__container {
    padding: 2rem;
  }
  
  .instructor-login__header h1 {
    font-size: 1.75rem;
  }
}
