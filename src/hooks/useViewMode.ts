import { useState, useEffect } from 'react';

type ViewMode = 'cards' | 'list' | 'grouped';

interface UseViewModeOptions {
  userId?: string;
  page: string;
  defaultMode?: ViewMode;
}

/**
 * Custom hook for persistent view mode preferences
 * Stores view mode per user per page in localStorage
 */
export const useViewMode = ({ 
  userId, 
  page, 
  defaultMode = 'cards' 
}: UseViewModeOptions) => {
  // Create a unique key for this user and page combination
  const storageKey = userId 
    ? `adminbuddy_viewmode_${userId}_${page}`
    : `adminbuddy_viewmode_guest_${page}`;

  // Initialize state with stored value or default
  const [viewMode, setViewModeState] = useState<ViewMode>(() => {
    try {
      const stored = localStorage.getItem(storageKey);
      return stored ? (stored as ViewMode) : defaultMode;
    } catch (error) {
      console.warn(`Error reading view mode from localStorage:`, error);
      return defaultMode;
    }
  });

  // Update localStorage when view mode changes
  const setViewMode = (mode: ViewMode) => {
    try {
      setViewModeState(mode);
      localStorage.setItem(storageKey, mode);
    } catch (error) {
      console.warn(`Error saving view mode to localStorage:`, error);
      // Still update state even if localStorage fails
      setViewModeState(mode);
    }
  };

  // Listen for changes from other tabs (same user, same page)
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === storageKey && e.newValue) {
        setViewModeState(e.newValue as ViewMode);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [storageKey]);

  // Update storage key if userId changes (user logs out/in)
  useEffect(() => {
    try {
      const stored = localStorage.getItem(storageKey);
      if (stored) {
        setViewModeState(stored as ViewMode);
      } else {
        setViewModeState(defaultMode);
      }
    } catch (error) {
      console.warn(`Error reading view mode after user change:`, error);
      setViewModeState(defaultMode);
    }
  }, [storageKey, defaultMode]);

  return { viewMode, setViewMode };
};
