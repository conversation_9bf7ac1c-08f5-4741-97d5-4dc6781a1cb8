import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { auth, db } from "../config/firebase";
import { User } from "firebase/auth";
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
} from "firebase/auth";
import { doc, getDoc, setDoc } from "firebase/firestore";
import {
  getUserProfile,
  createUserProfile,
  type UserProfile,
} from "../services/adminBuddyFirebaseService";

// UserProfile interface is imported from adminBuddyFirebaseService

interface TenantContextType {
  tenantId: string | null;
  user: User | null;
  userProfile: UserProfile | null;
  loading: boolean;
  setTenantId: (tenantId: string) => void;
  login: (
    email: string,
    password: string,
    rememberMe?: boolean
  ) => Promise<any>;
  signup: (email: string, password: string) => Promise<any>;
  logout: () => Promise<void>;
  getValidIdToken: () => Promise<string | null>; // For authenticated API calls
}

const TenantContext = createContext<TenantContextType | undefined>(undefined);

export const useTenant = () => {
  const context = useContext(TenantContext);
  if (context === undefined) {
    throw new Error("useTenant must be used within a TenantProvider");
  }
  return context;
};

interface TenantProviderProps {
  children: ReactNode;
}

export const TenantProvider: React.FC<TenantProviderProps> = ({ children }) => {
  const [tenantId, setTenantIdState] = useState<string | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [tokenRefreshInterval, setTokenRefreshInterval] =
    useState<NodeJS.Timeout | null>(null);

  const createDefaultUserProfile = async (user: User): Promise<UserProfile> => {
    // For demo purposes, create a default tenant for new users
    const defaultTenantId = `tenant-${user.uid.slice(0, 8)}`;

    // Initialize trial dates
    const now = new Date().toISOString();
    const trialEnd = new Date();
    trialEnd.setDate(trialEnd.getDate() + 14);
    const trialEndDate = trialEnd.toISOString();

    const profile: UserProfile = {
      uid: user.uid,
      email: user.email || "",
      tenantId: defaultTenantId,
      role: "owner", // First user becomes owner
      displayName: user.displayName || user.email?.split("@")[0] || "User",
      createdAt: now,
      lastLoginAt: now,
      // Trial Management
      trialStartDate: now,
      trialEndDate: trialEndDate,
      hasActiveSubscription: false,
    };

    // Save to Firestore
    await setDoc(doc(db, "user_profiles", user.uid), profile);
    console.log(
      "👤 Created default user profile with tenant:",
      defaultTenantId
    );

    return profile;
  };

  const loadUserProfile = async (user: User): Promise<UserProfile> => {
    try {
      const profileDoc = await getDoc(doc(db, "user_profiles", user.uid));

      if (profileDoc.exists()) {
        const profile = profileDoc.data() as UserProfile;

        // Check if existing user needs trial initialization
        let updatedProfile = { ...profile };
        if (!profile.trialStartDate || !profile.trialEndDate) {
          console.log("👤 Initializing trial for existing user:", user.email);
          const now = new Date().toISOString();
          const trialEnd = new Date();
          trialEnd.setDate(trialEnd.getDate() + 14);

          updatedProfile = {
            ...profile,
            trialStartDate: now,
            trialEndDate: trialEnd.toISOString(),
            hasActiveSubscription: profile.hasActiveSubscription || false,
          };
        }

        // Update last login and any missing trial data
        await setDoc(doc(db, "user_profiles", user.uid), {
          ...updatedProfile,
          lastLoginAt: new Date().toISOString(),
        });

        console.log(
          "👤 Loaded user profile for tenant:",
          updatedProfile.tenantId
        );
        return updatedProfile;
      } else {
        // Create new profile for first-time user
        return await createDefaultUserProfile(user);
      }
    } catch (error) {
      console.error("❌ Error loading user profile:", error);
      // Fallback to creating default profile
      return await createDefaultUserProfile(user);
    }
  };

  useEffect(() => {
    let mounted = true;

    const initializeAuth = async () => {
      console.log("🔍 Initializing pure REST auth (no Firebase SDK)...");

      // Check if we have localStorage auth data
      const authData = localStorage.getItem("adminbuddy_auth");
      if (authData) {
        console.log(
          "👤 Found localStorage auth - attempting to restore session"
        );
        try {
          const { email, uid, expiryTime, idToken } = JSON.parse(authData);

          // Check if token is still valid
          if (Date.now() < expiryTime) {
            console.log("✅ Token still valid, restoring session");

            // Try to get the user profile directly
            const profile = await getUserProfile(uid);
            if (profile && mounted) {
              console.log(
                "✅ Restored user session from localStorage:",
                profile.email
              );
              setUserProfile(profile);
              setTenantIdState(profile.tenantId);
              setUser({
                uid: profile.uid,
                email: profile.email,
              } as any);
              setLoading(false);

              // Start proactive token refresh for restored sessions
              startTokenRefreshInterval();
              return;
            }
          } else {
            console.log("⏰ Token expired, clearing localStorage");
            localStorage.removeItem("adminbuddy_auth");
          }
        } catch (error) {
          console.error("❌ Error restoring session from localStorage:", error);
          localStorage.removeItem("adminbuddy_auth");
        }
      }

      console.log("🔓 No valid session found - user needs to login");
      if (mounted) {
        setUserProfile(null);
        setTenantIdState(null);
        setUser(null);
        setLoading(false);
      }
    };

    // Listen for cross-tab logout events
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "adminbuddy_auth" && e.newValue === null) {
        console.log("🔓 Cross-tab logout detected");
        if (mounted) {
          setUser(null);
          setUserProfile(null);
          setTenantIdState(null);
          setLoading(false);
        }
      }
    };

    window.addEventListener("storage", handleStorageChange);
    initializeAuth();

    return () => {
      console.log("🧹 Cleaning up auth initialization");
      window.removeEventListener("storage", handleStorageChange);
      stopTokenRefreshInterval();
      mounted = false;
    };
  }, []);

  const handleUserLogin = async (user: any) => {
    console.log("👤 Processing user login:", user.email);
    try {
      // Try to load existing user profile
      let profile = await getUserProfile(user.uid);
      console.log("📋 Existing profile found:", profile ? "Yes" : "No");

      if (!profile) {
        // Create default profile for new users
        console.log("👤 Creating default profile for new user:", user.email);
        profile = await createUserProfile(user);
        console.log("✅ Default profile created:", profile);
      }

      setUser(user);
      setUserProfile(profile);
      setTenantIdState(profile.tenantId);
      setLoading(false);

      console.log("🏢 User login processed successfully:", profile.tenantId);
    } catch (error) {
      console.error("❌ Error processing user login:", error);
      throw error;
    }
  };

  const login = async (
    email: string,
    password: string,
    rememberMe: boolean = false
  ) => {
    console.log("🔐 Pure REST API login:", email);

    try {
      // Use Firebase Auth REST API directly
      const apiKey = "AIzaSyCDtTEuXAROJTlBmVe4GZggjDu_nWHsB_o";
      const url = `https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=${apiKey}`;

      console.log("📡 Making Firebase Auth REST API request...");

      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: email,
          password: password,
          returnSecureToken: true,
        }),
      });

      console.log("📡 Auth API Response status:", response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error("❌ Firebase Auth API error:", errorData);
        throw new Error(errorData.error?.message || "Login failed");
      }

      const authResult = await response.json();
      console.log("✅ Firebase Auth API success:", {
        localId: authResult.localId,
        email: authResult.email,
        expiresIn: authResult.expiresIn,
      });

      // Calculate token expiry - extend for "Remember Me" (kiosk mode)
      let expiryTime = Date.now() + parseInt(authResult.expiresIn) * 1000;

      if (rememberMe) {
        // For kiosks: extend session to 7 days using refresh token
        expiryTime = Date.now() + 7 * 24 * 60 * 60 * 1000; // 7 days
      }

      // Save auth data to localStorage for session persistence
      // ⚠️ SECURITY NOTE: localStorage is vulnerable to XSS attacks
      // For production, consider using HttpOnly cookies with a secure backend
      // or IndexedDB for slightly better security
      const authData = {
        uid: authResult.localId,
        email: authResult.email,
        idToken: authResult.idToken,
        refreshToken: authResult.refreshToken,
        expiryTime: expiryTime,
        loginTime: Date.now(),
        rememberMe: rememberMe,
      };
      localStorage.setItem("adminbuddy_auth", JSON.stringify(authData));
      console.log(
        "💾 Saved auth data to localStorage with expiry:",
        new Date(expiryTime)
      );

      // Get or create user profile (pass idToken for authentication)
      let userProfile = await getUserProfile(
        authResult.localId,
        authResult.idToken
      );
      if (!userProfile) {
        console.log("👤 Creating new user profile...");
        userProfile = await createUserProfile({
          uid: authResult.localId,
          email: authResult.email,
          emailVerified: authResult.emailVerified || false,
        });
      }

      // Set user state directly (no Firebase SDK involved)
      setUser({
        uid: authResult.localId,
        email: authResult.email,
      } as any);
      setUserProfile(userProfile);
      setTenantIdState(userProfile.tenantId);
      setLoading(false);

      // Start proactive token refresh for long-term sessions
      startTokenRefreshInterval();

      console.log("🎉 Pure REST login completed successfully!");
    } catch (error: any) {
      console.error("❌ Login error:", error);
      throw error;
    }
  };

  const signup = async (email: string, password: string) => {
    console.log("📝 Creating Firebase Auth account:", email);

    try {
      console.log("🚀 USING DIRECT API (Firebase SDK still broken)...");

      // Use direct API since Firebase SDK hangs in production
      const apiKey = "AIzaSyCDtTEuXAROJTlBmVe4GZggjDu_nWHsB_o";
      const url = `https://identitytoolkit.googleapis.com/v1/accounts:signUp?key=${apiKey}`;

      console.log("📡 Making direct Firebase Auth API request...");

      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: email,
          password: password,
          returnSecureToken: true,
        }),
      });

      console.log("📡 API Response status:", response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error("❌ Firebase Auth API error:", errorData);
        throw new Error(errorData.error?.message || "Signup failed");
      }

      const userData = await response.json();
      console.log("✅ Firebase Auth API success:", {
        localId: userData.localId,
        email: userData.email,
      });

      // Create a mock user object that matches Firebase Auth User interface
      const mockUser = {
        uid: userData.localId,
        email: userData.email,
        emailVerified: userData.emailVerified || false,
        displayName: userData.displayName || "",
        photoURL: userData.photoUrl || null,
        phoneNumber: userData.phoneNumber || null,
        providerId: "password",
        isAnonymous: false,
        metadata: {} as any,
        providerData: [],
        refreshToken: userData.refreshToken || "",
        tenantId: null,
        delete: async () => {},
        getIdToken: async () => userData.idToken || "",
        getIdTokenResult: async () => ({} as any),
        reload: async () => {},
        toJSON: () => ({}),
      } as User;

      console.log("👤 Created mock user object:", mockUser);

      // Calculate token expiry (Firebase tokens typically expire in 1 hour)
      const expiryTime =
        Date.now() + parseInt(userData.expiresIn || "3600") * 1000;

      // Save auth data to localStorage for session persistence
      const authData = {
        uid: userData.localId,
        email: userData.email,
        idToken: userData.idToken,
        refreshToken: userData.refreshToken,
        expiryTime: expiryTime,
        loginTime: Date.now(),
      };
      localStorage.setItem("adminbuddy_auth", JSON.stringify(authData));
      console.log(
        "💾 Saved auth data to localStorage for session persistence with expiry:",
        new Date(expiryTime)
      );

      // Manually handle user state since Firebase Auth SDK is broken
      await handleUserLogin(mockUser);
      console.log("🎉 Signup process completed successfully!");
    } catch (error: any) {
      console.error("❌ Signup error:", error);
      throw error;
    }
  };

  // Token refresh function following ChatGPT security best practices
  const refreshAuthToken = async (
    refreshToken: string
  ): Promise<{
    idToken: string;
    refreshToken: string;
    expiresIn: string;
  } | null> => {
    try {
      console.log("🔄 Refreshing auth token...");
      const apiKey = "AIzaSyCDtTEuXAROJTlBmVe4GZggjDu_nWHsB_o";
      const url = `https://securetoken.googleapis.com/v1/token?key=${apiKey}`;

      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          grant_type: "refresh_token",
          refresh_token: refreshToken,
        }),
      });

      if (!response.ok) {
        console.error("❌ Token refresh failed:", response.status);
        return null;
      }

      const data = await response.json();
      console.log("✅ Token refreshed successfully");

      return {
        idToken: data.id_token,
        refreshToken: data.refresh_token,
        expiresIn: data.expires_in,
      };
    } catch (error) {
      console.error("❌ Error refreshing token:", error);
      return null;
    }
  };

  // Proactive token refresh for long-term sessions (kiosks)
  const startTokenRefreshInterval = () => {
    // Clear any existing interval
    if (tokenRefreshInterval) {
      clearInterval(tokenRefreshInterval);
    }

    // Refresh token every 45 minutes (before 1-hour expiry)
    const interval = setInterval(async () => {
      console.log("⏰ Proactive token refresh triggered");
      const authData = localStorage.getItem("adminbuddy_auth");

      if (authData) {
        try {
          const { refreshToken } = JSON.parse(authData);
          const refreshResult = await refreshAuthToken(refreshToken);

          if (refreshResult) {
            const newExpiryTime =
              Date.now() + parseInt(refreshResult.expiresIn) * 1000;
            const updatedAuthData = {
              ...JSON.parse(authData),
              idToken: refreshResult.idToken,
              refreshToken: refreshResult.refreshToken,
              expiryTime: newExpiryTime,
            };
            localStorage.setItem(
              "adminbuddy_auth",
              JSON.stringify(updatedAuthData)
            );
            console.log(
              "✅ Proactive token refresh successful, expires:",
              new Date(newExpiryTime)
            );
          } else {
            console.log("❌ Proactive token refresh failed");
            clearInterval(interval);
          }
        } catch (error) {
          console.error("❌ Error in proactive token refresh:", error);
          clearInterval(interval);
        }
      } else {
        console.log("🔓 No auth data found, stopping token refresh");
        clearInterval(interval);
      }
    }, 45 * 60 * 1000); // 45 minutes

    setTokenRefreshInterval(interval);
    console.log("⏰ Started proactive token refresh (every 45 minutes)");
  };

  // Stop token refresh interval
  const stopTokenRefreshInterval = () => {
    if (tokenRefreshInterval) {
      clearInterval(tokenRefreshInterval);
      setTokenRefreshInterval(null);
      console.log("⏹️ Stopped proactive token refresh");
    }
  };

  // Get valid ID token (refresh if needed)
  const getValidIdToken = async (): Promise<string | null> => {
    const authData = localStorage.getItem("adminbuddy_auth");
    if (!authData) return null;

    try {
      const { idToken, refreshToken, expiryTime } = JSON.parse(authData);

      // Check if current token is still valid (with 5 min buffer)
      const bufferTime = 5 * 60 * 1000; // 5 minutes
      if (Date.now() < expiryTime - bufferTime) {
        return idToken;
      }

      // Token expired or about to expire, refresh it
      console.log("🔄 Token expired, refreshing...");
      const refreshResult = await refreshAuthToken(refreshToken);

      if (refreshResult) {
        // Update localStorage with new tokens
        const newExpiryTime =
          Date.now() + parseInt(refreshResult.expiresIn) * 1000;
        const updatedAuthData = {
          ...JSON.parse(authData),
          idToken: refreshResult.idToken,
          refreshToken: refreshResult.refreshToken,
          expiryTime: newExpiryTime,
        };
        localStorage.setItem(
          "adminbuddy_auth",
          JSON.stringify(updatedAuthData)
        );
        console.log("💾 Updated localStorage with refreshed tokens");

        return refreshResult.idToken;
      } else {
        // Refresh failed, clear invalid data
        console.log("❌ Token refresh failed, clearing auth data");
        localStorage.removeItem("adminbuddy_auth");
        return null;
      }
    } catch (error) {
      console.error("❌ Error getting valid token:", error);
      localStorage.removeItem("adminbuddy_auth");
      return null;
    }
  };

  const logout = async () => {
    console.log("🔓 Pure REST logout");
    try {
      // Stop proactive token refresh
      stopTokenRefreshInterval();

      // Clear localStorage auth data (following security best practices)
      localStorage.removeItem("adminbuddy_auth");
      console.log("🧹 Cleared localStorage auth data");

      // Clear user state directly (no Firebase SDK involved)
      setUser(null);
      setUserProfile(null);
      setTenantIdState(null);
      setLoading(false);

      console.log("✅ Pure REST logout successful");
    } catch (error) {
      console.error("❌ Logout error:", error);
      // Still clear localStorage even if something fails
      stopTokenRefreshInterval();
      localStorage.removeItem("adminbuddy_auth");
      throw error;
    }
  };

  const setTenantId = (newTenantId: string) => {
    setTenantIdState(newTenantId);
    console.log("🏢 Tenant context updated:", newTenantId);
  };

  const value: TenantContextType = {
    tenantId,
    user,
    userProfile,
    loading,
    setTenantId,
    login,
    signup,
    logout,
    getValidIdToken, // Export for authenticated API calls
  };

  return (
    <TenantContext.Provider value={value}>{children}</TenantContext.Provider>
  );
};

export default TenantContext;
