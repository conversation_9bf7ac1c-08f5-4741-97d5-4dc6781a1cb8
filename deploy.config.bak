# Bit By Bit Deployment Configuration
# Copy this file to deploy.config and update with your values

# Required: Your S3 bucket name
BUCKET_NAME=bitbybit-class

# Optional: AWS region (default: us-east-1)
REGION=us-east-1

# HTTPS Configuration
# Set to true to enable HTTPS via CloudFront (recommended)
SETUP_HTTPS=true

# Optional: Your custom domain name (for HTTPS with custom domain)
# Leave empty to use CloudFront's default domain with free SSL
DOMAIN_NAME=

# Optional: Existing SSL certificate ARN (auto-generated if not provided)
CERTIFICATE_ARN=

# Optional: Existing CloudFront distribution ID (auto-created if not provided)
CLOUDFRONT_DISTRIBUTION_ID=

# Example configurations:

# Basic HTTPS (CloudFront default domain):
# BUCKET_NAME=bitbybit-website-prod
# SETUP_HTTPS=true

# Custom domain with HTTPS:
# BUCKET_NAME=bitbybit-website-prod
# DOMAIN_NAME=bitbybit.com
# SETUP_HTTPS=true

# HTTP only (not recommended for production):
# BUCKET_NAME=bitbybit-website-prod
# SETUP_HTTPS=false
