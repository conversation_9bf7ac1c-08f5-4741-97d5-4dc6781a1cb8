# Deployment Guide - Bit By Bit Website

## 🚀 Automated Production Deployment

### Quick Start (Recommended)

```bash
# 1. Setup deployment configuration (one-time)
npm run deploy:setup

# 2. Edit deploy.config with your bucket name
# BUCKET_NAME=your-bucket-name-here

# 3. Deploy to production
npm run deploy
```

That's it! The script handles everything automatically.

## 📋 Prerequisites

- AWS CLI installed and configured (`aws configure`)
- Node.js and npm installed
- Appropriate AWS permissions for S3 and CloudFront

### AWS Permissions Required

Your AWS user/role needs these permissions:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:CreateBucket",
        "s3:DeleteObject",
        "s3:GetObject",
        "s3:PutObject",
        "s3:PutBucketPolicy",
        "s3:PutBucketWebsite",
        "s3:PutPublicAccessBlock",
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::your-bucket-name",
        "arn:aws:s3:::your-bucket-name/*"
      ]
    },
    {
      "Effect": "Allow",
      "Action": ["cloudfront:CreateInvalidation"],
      "Resource": "*"
    }
  ]
}
```

## 🛠️ Deployment Methods

### Method 1: Automated Script (Recommended)

**Setup (one-time):**

```bash
npm run deploy:setup
# Edit deploy.config with your settings
```

**Deploy:**

```bash
npm run deploy
```

### Method 2: Manual Commands

```bash
# Build for production
npm run build:production

# Deploy manually
aws s3 sync build/ s3://your-bucket-name --delete
aws s3 website s3://your-bucket-name --index-document index.html --error-document index.html
```

### Method 3: Platform-Specific Scripts

**Linux/macOS:**

```bash
./scripts/deploy.sh
```

**Windows PowerShell:**

```powershell
.\scripts\deploy.ps1
```

## 🤖 What the Automated Script Does

The deployment script automatically handles:

### ✅ **Build Process**

- Cleans previous builds
- Installs dependencies if needed
- Creates production build with all dev tools removed
- Verifies build success

### ✅ **AWS Configuration**

- Checks if bucket exists (creates if needed)
- Configures bucket policy for public read access
- Enables static website hosting
- Sets proper public access settings
- Configures error document for SPA routing

### ✅ **Deployment**

- Syncs files to S3 with optimal cache headers
- Sets long cache for static assets (CSS, JS, images)
- Sets no-cache for HTML files (for updates)
- Deletes removed files from S3

### ✅ **Verification**

- Tests website accessibility
- Verifies no development tools are visible
- Confirms production build integrity
- Provides website URL

### ✅ **Optional Features**

- CloudFront cache invalidation (if configured)
- Colored output for easy reading
- Detailed progress reporting
- Error handling and rollback

## 📁 Configuration

### deploy.config File

```bash
# Required: Your S3 bucket name
BUCKET_NAME=bitbybit-website-prod

# Optional: AWS region (default: us-east-1)
REGION=us-west-2

# HTTPS Configuration (recommended)
SETUP_HTTPS=true

# Optional: Custom domain for HTTPS
DOMAIN_NAME=bitbybit.com

# Auto-generated (leave empty for first deployment)
CERTIFICATE_ARN=
CLOUDFRONT_DISTRIBUTION_ID=
```

## 🔒 Deployment Configuration Options

### Option 1: AWS Free Tier (Recommended for Low Volume) 💰

```bash
BUCKET_NAME=your-bucket-name
REGION=us-east-1
SETUP_HTTPS=false
```

**Result:** Free HTTP website at `http://your-bucket-name.s3-website-us-east-1.amazonaws.com`
**Cost:** $0/month (within free tier limits)
**Perfect for:** Personal sites, small businesses, learning projects, low-traffic sites

### Option 2: CloudFront Default Domain (HTTPS)

```bash
BUCKET_NAME=your-bucket-name
SETUP_HTTPS=true
# Leave DOMAIN_NAME empty
```

**Result:** HTTPS at `https://d1234567890abc.cloudfront.net`
**Cost:** ~$1-5/month (CloudFront charges apply)
**Perfect for:** Sites needing HTTPS without custom domain

### Option 3: Custom Domain with HTTPS (Production)

```bash
BUCKET_NAME=your-bucket-name
SETUP_HTTPS=true
DOMAIN_NAME=yourdomain.com
```

**Result:** HTTPS at `https://yourdomain.com` and `https://www.yourdomain.com`
**Cost:** ~$1.50-5.50/month (CloudFront + Route 53 charges)
**Perfect for:** Professional sites with custom branding

## 📜 SSL Certificate Details

### Automatic Certificate Management

- **Free SSL certificates** from AWS Certificate Manager (ACM)
- **Auto-renewal** - certificates renew automatically before expiration
- **Long-lasting** - certificates are valid for 13 months and auto-renew
- **Wildcard support** - covers both `yourdomain.com` and `www.yourdomain.com`

### Certificate Validation Process

1. **DNS Validation** (recommended) - Add CNAME records to your DNS
2. **Email Validation** - Respond to validation emails
3. **Automatic validation** for Route 53 hosted domains

### Certificate Lifecycle

- **Initial Request:** Script requests certificate automatically
- **Validation:** You complete DNS/email validation (one-time)
- **Deployment:** Certificate applied to CloudFront distribution
- **Renewal:** AWS handles automatic renewal (no action required)

### Security Features

- **TLS 1.2+ only** - Modern encryption standards
- **Perfect Forward Secrecy** - Enhanced security
- **HSTS support** - HTTP Strict Transport Security
- **Automatic HTTP to HTTPS redirect**

### What Gets Removed in Production

The production build automatically removes ALL performance monitoring features:

✅ **Completely Hidden:**

- Performance Dashboard (📊 button)
- Performance Test Component
- Console performance logs
- Web Vitals tracking
- Component render time monitoring
- Memory usage tracking

✅ **Zero Performance Impact:**

- All monitoring hooks return early in production
- No performance overhead
- No console logs
- No development tools visible

### Deploy to S3

1. **Build the project:**

   ```bash
   npm run build:production
   ```

2. **Upload to S3:**

   ```bash
   aws s3 sync build/ s3://your-bucket-name --delete
   ```

3. **Set proper permissions:**

   ```bash
   aws s3api put-bucket-policy --bucket your-bucket-name --policy '{
     "Version": "2012-10-17",
     "Statement": [
       {
         "Sid": "PublicReadGetObject",
         "Effect": "Allow",
         "Principal": "*",
         "Action": "s3:GetObject",
         "Resource": "arn:aws:s3:::your-bucket-name/*"
       }
     ]
   }'
   ```

4. **Enable static website hosting:**
   ```bash
   aws s3 website s3://your-bucket-name --index-document index.html --error-document index.html
   ```

### Environment Variables

For production deployment, ensure these are set:

```bash
NODE_ENV=production
REACT_APP_ENV=production
```

### Verification

After deployment, verify that:

1. ✅ No 📊 performance dashboard button visible
2. ✅ No performance test component on home page
3. ✅ No console logs related to performance
4. ✅ All pages load correctly
5. ✅ Navigation works properly
6. ✅ Course details load correctly

### CloudFront Configuration (Recommended)

For better performance and HTTPS, set up CloudFront:

1. Create CloudFront distribution
2. Set origin to your S3 bucket
3. Configure error pages to redirect to `/index.html` for SPA routing
4. Enable compression
5. Set appropriate cache behaviors

### Build Output

The production build creates optimized files in the `build/` directory:

- Minified JavaScript and CSS
- Optimized images
- Service worker for caching
- All development tools removed

### Performance Monitoring in Production

While the development monitoring tools are removed, you can still monitor production performance using:

- Google Analytics
- AWS CloudWatch
- Third-party monitoring services
- Browser's built-in Performance API

### Troubleshooting

**Issue: Performance tools still visible**

- Solution: Ensure `NODE_ENV=production` is set during build

**Issue: Routing doesn't work on S3**

- Solution: Set error document to `index.html` in S3 website configuration

**Issue: CORS errors**

- Solution: Configure proper CORS policy on S3 bucket

### Security Considerations

- All development tools are automatically removed
- No sensitive development information exposed
- Proper S3 bucket policies applied
- HTTPS recommended via CloudFront
