rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Public read access to course capacities
    match /courseCapacities/{courseId} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.token.email == '<EMAIL>';
    }



    // Admin-only access to admin settings
    match /adminSettings/{document} {
      allow read, write: if request.auth != null && request.auth.token.email == '<EMAIL>';
    }

    // Admin-only access to categories
    match /categories/{categoryId} {
      allow read, write: if request.auth != null && request.auth.token.email == '<EMAIL>';
    }



    // Admin-only access to email templates
    match /emailTemplates/{templateId} {
      allow read, write: if request.auth != null && request.auth.token.email == '<EMAIL>';
    }

    // Admin-only access to instructor management, plus instructors can manage their own document
    match /instructors/{instructorId} {
      allow read, write: if request.auth != null && request.auth.token.email == '<EMAIL>';
      // Allow instructors to create and update their own document during account setup
      allow create, update: if request.auth != null && request.auth.uid == instructorId;
      // Allow instructors to read their own document (by ID or by email)
      allow read: if request.auth != null && (
        request.auth.uid == instructorId ||
        resource.data.email == request.auth.token.email
      );
      // Allow instructors to delete temporary documents during setup (when email matches)
      allow delete: if request.auth != null &&
        resource.data.email == request.auth.token.email &&
        resource.data.status == 'invited';
      // Allow instructors to list/query documents to find their own by email
      allow list: if request.auth != null;
    }



    // Admin access to all courses, instructors can read courses they're assigned to
    match /courses/{courseId} {
      allow read, write: if request.auth != null && request.auth.token.email == '<EMAIL>';
      // Allow public read access for course browsing
      allow read: if true;
      // Allow instructors to read courses they are assigned to (course-centric approach)
      allow read: if request.auth != null &&
        exists(/databases/$(database)/documents/instructors/$(request.auth.uid)) &&
        resource.data.instructorId == request.auth.uid;
      // Allow instructors to query courses to find their assigned courses
      allow list: if request.auth != null &&
        exists(/databases/$(database)/documents/instructors/$(request.auth.uid));
    }

    // Admin access to all registrations, instructors can read registrations for their assigned courses
    match /registrations/{registrationId} {
      allow read, write: if request.auth != null && request.auth.token.email == '<EMAIL>';
      // Allow instructors to read registrations for courses they are assigned to
      allow read: if request.auth != null &&
        exists(/databases/$(database)/documents/instructors/$(request.auth.uid)) &&
        resource.data.courseId in get(/databases/$(database)/documents/instructors/$(request.auth.uid)).data.assignedCourses;
      // Allow instructors to query registrations for their assigned courses
      allow list: if request.auth != null &&
        exists(/databases/$(database)/documents/instructors/$(request.auth.uid));
    }

    // Instructor and admin access to attendance records for assigned courses
    match /attendance/{attendanceId} {
      allow read, write: if request.auth != null && (
        request.auth.token.email == '<EMAIL>' ||
        (exists(/databases/$(database)/documents/instructors/$(request.auth.uid)) &&
         resource.data.courseId in get(/databases/$(database)/documents/instructors/$(request.auth.uid)).data.assignedCourses)
      );
      // Allow instructors to query attendance records for their assigned courses
      allow list: if request.auth != null && (
        request.auth.token.email == '<EMAIL>' ||
        exists(/databases/$(database)/documents/instructors/$(request.auth.uid))
      );
    }

    // Instructor and admin access to student progress for assigned courses
    match /student_progress/{progressId} {
      // Admin has full access
      allow read, write: if request.auth != null && request.auth.token.email == '<EMAIL>';

      // Temporary: Allow any authenticated user (for debugging)
      allow read, write: if request.auth != null;

      // Allow instructors to query student progress
      allow list: if request.auth != null;
    }

    // Admin and instructor access to course sessions
    match /courseSessions/{sessionId} {
      // Admin has full access
      allow read, write: if request.auth != null && request.auth.token.email == '<EMAIL>';

      // Temporary: Allow any authenticated user (for debugging)
      allow read, write: if request.auth != null;

      // Allow instructors to query sessions
      allow list: if request.auth != null;

      // Public read access for upcoming sessions (for customer calendar view)
      allow read: if resource.data.status == 'scheduled' &&
        resource.data.date >= string(timestamp.date(request.time));
    }

    // AdminBuddy Collections - Tenant-based Security Rules
    // Users can only access data for their own tenant

    // Helper function to get user's tenant ID
    function getUserTenantId() {
      return get(/databases/$(database)/documents/user_profiles/$(request.auth.uid)).data.tenantId;
    }

    // Helper function to check if user is authenticated and has tenant access
    function hasValidTenantAccess(tenantId) {
      return request.auth != null &&
             exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
             getUserTenantId() == tenantId;
    }

    // User Profiles (SaaS Multi-Tenant) - Users can only access their own profile
    match /user_profiles/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      // Allow creation during signup
      allow create: if request.auth != null && request.auth.uid == userId;
      // Allow get operations for authenticated users accessing their own profile
      allow get: if request.auth != null && request.auth.uid == userId;
    }

    // Locations - Tenant-specific access
    match /adminbuddy_locations/{locationId} {
      allow read: if hasValidTenantAccess(resource.data.tenantId);
      allow create: if request.auth != null &&
                   exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow update: if hasValidTenantAccess(resource.data.tenantId) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow delete: if hasValidTenantAccess(resource.data.tenantId);
      allow list: if request.auth != null &&
                 exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid));
    }

    // Routines - Tenant-specific access
    match /adminbuddy_routines/{routineId} {
      allow read: if hasValidTenantAccess(resource.data.tenantId);
      allow create: if request.auth != null &&
                   exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow update: if hasValidTenantAccess(resource.data.tenantId) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow delete: if hasValidTenantAccess(resource.data.tenantId);
      allow list: if request.auth != null &&
                 exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid));
    }

    // Routine Tasks - Tenant-specific access
    match /adminbuddy_routine_tasks/{taskId} {
      allow read: if hasValidTenantAccess(resource.data.tenantId);
      allow create: if request.auth != null &&
                   exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow update: if hasValidTenantAccess(resource.data.tenantId) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow delete: if hasValidTenantAccess(resource.data.tenantId);
      allow list: if request.auth != null &&
                 exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid));
    }

    // Location Routine Schedules - Tenant-specific access
    match /adminbuddy_location_routine_schedules/{scheduleId} {
      allow read: if hasValidTenantAccess(resource.data.tenantId);
      allow create: if request.auth != null &&
                   exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow update: if hasValidTenantAccess(resource.data.tenantId) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow delete: if hasValidTenantAccess(resource.data.tenantId);
      allow list: if request.auth != null &&
                 exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid));
    }

    // Daily Tasks - Tenant-specific access
    match /adminbuddy_daily_tasks/{taskId} {
      allow read: if hasValidTenantAccess(resource.data.tenantId);
      allow create: if request.auth != null &&
                   exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow update: if hasValidTenantAccess(resource.data.tenantId) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow delete: if hasValidTenantAccess(resource.data.tenantId);
      allow list: if request.auth != null &&
                 exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid));
    }

    // Ad-hoc Tasks - Tenant-specific access
    match /adminbuddy_ad_hoc_tasks/{taskId} {
      allow read: if hasValidTenantAccess(resource.data.tenantId);
      allow create: if request.auth != null &&
                   exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow update: if hasValidTenantAccess(resource.data.tenantId) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow delete: if hasValidTenantAccess(resource.data.tenantId);
      allow list: if request.auth != null &&
                 exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid));
    }

    // Tenant Roles - Tenant-specific access
    match /adminbuddy_tenant_roles/{roleId} {
      allow read: if hasValidTenantAccess(resource.data.tenantId);
      allow create: if request.auth != null &&
                   exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow update: if hasValidTenantAccess(resource.data.tenantId) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow delete: if hasValidTenantAccess(resource.data.tenantId);
      allow list: if request.auth != null &&
                 exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid));
    }

    // Location Reports - Tenant-specific access
    match /adminbuddy_location_reports/{reportId} {
      allow read: if hasValidTenantAccess(resource.data.tenantId);
      allow create: if request.auth != null &&
                   exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow update: if hasValidTenantAccess(resource.data.tenantId) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow delete: if hasValidTenantAccess(resource.data.tenantId);
      allow list: if request.auth != null &&
                 exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid));
    }

    // Legacy Collections (for backward compatibility) - Tenant-specific access
    match /adminbuddy_tasks/{document} {
      allow read: if hasValidTenantAccess(resource.data.tenantId);
      allow create: if request.auth != null &&
                   exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow update: if hasValidTenantAccess(resource.data.tenantId) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow delete: if hasValidTenantAccess(resource.data.tenantId);
      allow list: if request.auth != null &&
                 exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid));
    }

    match /adminbuddy_location_routines/{document} {
      allow read: if hasValidTenantAccess(resource.data.tenantId);
      allow create: if request.auth != null &&
                   exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow update: if hasValidTenantAccess(resource.data.tenantId) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow delete: if hasValidTenantAccess(resource.data.tenantId);
      allow list: if request.auth != null &&
                 exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid));
    }

    match /adminbuddy_task_completions/{document} {
      allow read: if hasValidTenantAccess(resource.data.tenantId);
      allow create: if request.auth != null &&
                   exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow update: if hasValidTenantAccess(resource.data.tenantId) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow delete: if hasValidTenantAccess(resource.data.tenantId);
      allow list: if request.auth != null &&
                 exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid));
    }
  }
}