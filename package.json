{"name": "adminbuddy-site", "version": "1.0.0", "private": true, "dependencies": {"@aws-sdk/client-s3": "^3.817.0", "@aws-sdk/client-sts": "^3.817.0", "@aws-sdk/s3-request-presigner": "^3.817.0", "@stripe/stripe-js": "^7.3.1", "@types/react-beautiful-dnd": "^13.1.8", "chart.js": "^4.4.9", "cross-env": "^7.0.3", "date-fns": "^2.29.0", "firebase": "^11.8.1", "react": "^18.0.0", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "react-scripts": "^5.0.1"}, "scripts": {"start": "REACT_APP_PERFORMANCE_MODE=false react-scripts start", "start:performance": "REACT_APP_PERFORMANCE_MODE=true react-scripts start", "start:win": "set REACT_APP_PERFORMANCE_MODE=false&& react-scripts start", "start:performance:win": "set REACT_APP_PERFORMANCE_MODE=true&& react-scripts start", "build": "NODE_ENV=production react-scripts build", "build:production": "NODE_ENV=production REACT_APP_ENV=production react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "deploy": "./scripts/deploy.sh", "deploy:setup": "cp deploy.config.example deploy.config && echo 'Please edit deploy.config with your settings'", "firebase:config": "node scripts/firebase-config.js", "firebase:setup": "node scripts/firebase-setup.js", "firebase:create-admin": "node scripts/firebase-create-admin.js", "firebase:init-data": "node scripts/firebase-init-data.js", "firebase:init-email-templates": "node scripts/firebase-init-email-templates.js"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/node": "^16.18.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/react-router-dom": "^5.3.3", "typescript": "^4.9.5"}}