# AWS Free Tier Deployment Guide

## 💰 Staying Within AWS Free Tier

### **🎯 Free Tier Limits (12 months from account creation)**

**Amazon S3:**
- ✅ **5 GB storage** - More than enough for a website
- ✅ **20,000 GET requests** - ~650 page views per day
- ✅ **2,000 PUT requests** - Unlimited deployments
- ✅ **15 GB data transfer out** - ~500 page views per day

**What's NOT Free Tier:**
- ❌ **CloudFront** - CDN service (costs ~$0.085/GB after 1TB free)
- ❌ **Route 53** - DNS service ($0.50/month per hosted zone)
- ❌ **Certificate Manager** - Free, but requires CloudFront/Load Balancer

## 🔧 **Free Tier Configuration**

### **Optimal Free Tier Setup:**
```bash
# deploy.config
BUCKET_NAME=bitbybit-website
REGION=us-east-1
SETUP_HTTPS=false
```

**Result:**
- ✅ **$0/month** - Completely free
- ✅ **HTTP website** - Fully functional
- ✅ **Global accessibility** - Available worldwide
- ❌ **No HTTPS** - HTTP only (not ideal for production)

## 📊 **Cost Comparison**

### **Free Tier (S3 Only):**
```
Monthly Cost: $0.00
- S3 Storage (5GB): Free
- S3 Requests (20K GET): Free
- Data Transfer (15GB): Free
- Total: FREE ✅
```

### **With CloudFront (Not Free):**
```
Monthly Cost: ~$1-5
- S3 Storage: Free (within limits)
- CloudFront (1TB): Free first month, then $0.085/GB
- SSL Certificate: Free
- Data Transfer: Varies by region
- Total: $1-5/month ❌
```

### **With Custom Domain (Not Free):**
```
Monthly Cost: ~$1.50-5.50
- Route 53 Hosted Zone: $0.50/month
- CloudFront: $1-5/month
- Domain registration: $10-15/year
- Total: $1.50-5.50/month ❌
```

## 🚀 **Free Tier Deployment**

### **Step 1: Configure for Free Tier**
```bash
npm run deploy:setup
```

Edit `deploy.config`:
```bash
BUCKET_NAME=bitbybit-website
REGION=us-east-1
SETUP_HTTPS=false
```

### **Step 2: Deploy**
```bash
npm run deploy
```

### **Step 3: Access Your Site**
```
http://bitbybit-website.s3-website-us-east-1.amazonaws.com
```

## 📈 **Free Tier Monitoring**

### **Track Your Usage:**
1. **AWS Billing Dashboard** - Monitor free tier usage
2. **S3 Metrics** - Storage and request counts
3. **CloudWatch** - Basic monitoring (free tier included)

### **Usage Estimates for Low Volume Site:**
```
Typical Small Website:
- Size: 10-50 MB (well under 5GB limit)
- Monthly visitors: 100-500 (well under 20K requests)
- Page views: 500-2000 (well under limits)
- Data transfer: 1-5 GB (well under 15GB limit)

Result: Stays comfortably within free tier ✅
```

## ⚠️ **Free Tier Limitations**

### **What You Get:**
- ✅ **Static website hosting** - HTML, CSS, JS, images
- ✅ **Global accessibility** - Available worldwide
- ✅ **Reliable hosting** - AWS infrastructure
- ✅ **Custom error pages** - 404 handling
- ✅ **Fast deployment** - 30-60 seconds

### **What You Don't Get:**
- ❌ **HTTPS/SSL** - HTTP only
- ❌ **Custom domain** - Uses AWS domain
- ❌ **Global CDN** - No edge caching
- ❌ **Advanced caching** - Basic S3 caching only

## 🔒 **HTTPS Alternatives for Free Tier**

### **Option 1: Cloudflare (Free)**
1. **Point domain to Cloudflare** - Free DNS + CDN
2. **Configure Cloudflare** - Point to S3 website URL
3. **Enable SSL** - Free SSL certificate
4. **Result:** Free HTTPS + CDN + custom domain

### **Option 2: Netlify (Free Tier)**
1. **Connect GitHub repo** - Automatic deployments
2. **Custom domain** - Free SSL included
3. **Global CDN** - Free tier includes CDN
4. **Result:** Free HTTPS + CDN + custom domain

### **Option 3: GitHub Pages (Free)**
1. **GitHub repository** - Public repos are free
2. **Custom domain** - Free SSL included
3. **Automatic deployment** - Push to deploy
4. **Result:** Free HTTPS + custom domain

## 🎯 **Recommended Free Tier Strategy**

### **Phase 1: Start with AWS Free Tier**
```bash
# Free S3 hosting
SETUP_HTTPS=false
# Cost: $0/month
# Perfect for: Testing, development, low-traffic sites
```

### **Phase 2: Add Free HTTPS (Cloudflare)**
```bash
# S3 + Cloudflare
# Cost: $0/month (both have free tiers)
# Perfect for: Production sites with custom domain
```

### **Phase 3: Upgrade to AWS CloudFront (When Needed)**
```bash
# Full AWS stack
SETUP_HTTPS=true
# Cost: $1-5/month
# Perfect for: High-traffic, professional sites
```

## 📋 **Free Tier Checklist**

### **Before Deployment:**
- [ ] ✅ AWS account is less than 12 months old
- [ ] ✅ `SETUP_HTTPS=false` in deploy.config
- [ ] ✅ `REGION=us-east-1` (lowest cost region)
- [ ] ✅ Bucket name is globally unique

### **After Deployment:**
- [ ] ✅ Site accessible via S3 website URL
- [ ] ✅ All pages load correctly
- [ ] ✅ Images and assets load properly
- [ ] ✅ No 404 errors on navigation

### **Ongoing Monitoring:**
- [ ] ✅ Check AWS billing dashboard monthly
- [ ] ✅ Monitor S3 usage metrics
- [ ] ✅ Stay under 20K requests/month
- [ ] ✅ Keep total storage under 5GB

## 🚨 **Avoiding Unexpected Charges**

### **Common Cost Traps:**
1. **CloudFront enabled** - Disable with `SETUP_HTTPS=false`
2. **Wrong region** - Use `us-east-1` for lowest costs
3. **Large files** - Optimize images and assets
4. **High traffic** - Monitor request counts

### **Cost Monitoring:**
```bash
# Set up billing alerts
AWS Console → Billing → Billing Preferences → Receive Billing Alerts

# Set alert for $1 threshold
# Get notified before any charges occur
```

## 💡 **Optimization Tips**

### **Reduce Storage Usage:**
- **Optimize images** - Use WebP format, compress images
- **Minify assets** - Compress CSS/JS files
- **Remove unused files** - Clean up old deployments

### **Reduce Request Counts:**
- **Browser caching** - Set proper cache headers
- **Combine files** - Fewer requests per page
- **Optimize images** - Reduce file sizes

### **Monitor Usage:**
```bash
# Check S3 usage
aws s3api get-bucket-location --bucket your-bucket-name
aws s3 ls s3://your-bucket-name --recursive --human-readable --summarize
```

## 🎉 **Free Tier Benefits**

### **Perfect for:**
- ✅ **Personal websites** - Portfolios, blogs, landing pages
- ✅ **Small businesses** - Local businesses, startups
- ✅ **Learning projects** - Educational, experimental sites
- ✅ **Low-traffic sites** - Under 500 visitors/month

### **Success Stories:**
- **Personal portfolios** - Showcase your work
- **Small business sites** - Local restaurants, services
- **Event websites** - Conferences, meetups
- **Documentation sites** - Project documentation

**Your website can run completely free on AWS for the first 12 months!** 💰✨

After 12 months, costs are typically $1-3/month for low-volume sites, which is still very affordable.
