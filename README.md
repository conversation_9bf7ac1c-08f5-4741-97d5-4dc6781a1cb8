# Bit By Bit - Coding Classes Website

A modern, responsive React website for Bit By Bit coding classes, built with TypeScript and featuring comprehensive performance monitoring in development.

## 🚀 Quick Start

```bash
# Clone the repository
git clone <repository-url>
cd bitbybit-site

# Install dependencies
npm install

# Start development server (clean, no performance monitoring)
npm start

# OR start with performance monitoring enabled
npm run start:performance

# Windows users (if the above don't work):
npm run start:win
npm run start:performance:win

# Open http://localhost:3000
```

### Development Modes

**Standard Development (`npm start`)**

- Clean development experience
- No performance monitoring or dashboards
- Faster startup and runtime
- Recommended for content development and general work

**Performance Mode (`npm run start:performance`)**

- Enables performance monitoring dashboard
- Component render time tracking
- Web Vitals monitoring
- Performance warnings in console
- Use when optimizing performance or debugging slow renders

## 💰 AWS Free Tier Deployment

For low-volume sites, you can deploy completely free using AWS Free Tier:

```bash
# Setup for free tier
npm run deploy:setup

# Edit deploy.config:
# BUCKET_NAME=your-site-name
# REGION=us-east-1
# SETUP_HTTPS=false

# Deploy for free
npm run deploy
```

**Free Tier Includes:**

- ✅ 5GB storage (plenty for most websites)
- ✅ 20,000 requests/month (~650 page views/day)
- ✅ 15GB data transfer/month
- ✅ HTTP website hosting
- ✅ $0/month cost

See [AWS_FREE_TIER_GUIDE.md](AWS_FREE_TIER_GUIDE.md) for complete details.

## 📁 Project Structure

```
src/
├── components/           # Reusable UI components
│   ├── ui/              # Basic UI components
│   │   ├── Button/      # Reusable button component
│   │   ├── ErrorBoundary/ # Error handling wrapper
│   │   ├── LoadingSpinner/ # Loading indicators
│   │   └── LoadingSkeleton/ # Skeleton loading screens
│   ├── layout/          # Layout components
│   │   ├── Header/      # Navigation header with mobile menu
│   │   ├── Footer/      # Site footer
│   │   └── Layout/      # Main layout wrapper
│   ├── course/          # Course-related components
│   │   ├── CourseCard/  # Individual course display
│   │   ├── CourseCardSkeleton/ # Loading state for courses
│   │   └── CalendarCard/ # Calendar view for courses
│   ├── hoc/             # Higher-order components
│   │   └── withPerformanceMonitoring.tsx
│   └── dev/             # Development-only components
│       ├── PerformanceDashboard/ # Performance monitoring UI
│       └── PerformanceTest/ # Performance testing component
├── hooks/               # Custom React hooks
│   ├── useCourses.ts    # Course data management
│   ├── useLocalStorage.ts # Persistent state management
│   ├── useScrollToElement.ts # Scroll functionality
│   ├── useImagePreload.ts # Image optimization
│   ├── useViewport.ts   # Responsive breakpoint detection
│   ├── useIntersectionObserver.ts # Scroll-based animations
│   ├── useAnimation.ts  # Motion-preference-aware animations
│   └── usePerformance.ts # Performance monitoring
├── pages/               # Page components (lazy-loaded)
│   ├── Home/           # Landing page
│   ├── Courses/        # Course listing page
│   ├── Calendar/       # Course calendar view
│   ├── CourseDetail/   # Individual course details
│   └── Shop/           # Merchandise page
├── styles/             # Global styles and utilities
│   ├── globals.css     # Base styles and imports
│   ├── animations.css  # Animation utilities
│   └── responsive.css  # Responsive utilities
├── types/              # TypeScript type definitions
├── constants/          # Application constants
├── data/               # Static data and content
├── utils/              # Utility functions
└── App.tsx             # Main application component
```

## 🛠️ Available Scripts

### Development

```bash
npm start              # Start development server (clean, no performance tools)
npm run start:performance # Start with performance monitoring enabled
npm run start:win      # Windows version (if needed)
npm run start:performance:win # Windows performance mode (if needed)
npm test               # Run test suite
npm run eject          # Eject from Create React App (irreversible)
```

### Production

```bash
npm run build          # Standard production build
npm run build:production # Production build with explicit env vars
```

### Deployment

```bash
npm run deploy:setup   # Setup deployment configuration (one-time)
npm run deploy         # Automated deployment to AWS S3
```

### Testing Production Build

```bash
# After building, test the production version locally
npx serve -s build -p 3001
# Open http://localhost:3001
```

## 🎯 Key Features

### 🏗️ **Modern Architecture**

- **TypeScript** - Full type safety and better developer experience
- **React 18** - Latest React features with concurrent rendering
- **React Router v6** - Modern routing with lazy loading
- **Custom Hooks** - Reusable logic for data management and UI interactions

### ⚡ **Performance Optimizations**

- **Lazy Loading** - Pages loaded on-demand for faster initial load
- **Image Optimization** - Preloading and lazy loading for images
- **Component Memoization** - Optimized re-rendering with React.memo
- **Intersection Observer** - Efficient scroll-based animations
- **Responsive Design** - Mobile-first approach with optimized breakpoints

### 🛡️ **Error Handling & Resilience**

- **Error Boundaries** - Graceful error handling with recovery options
- **Loading States** - Skeleton screens and loading indicators
- **Fallback UI** - User-friendly error messages and retry mechanisms

### ♿ **Accessibility**

- **ARIA Attributes** - Proper semantic markup and screen reader support
- **Keyboard Navigation** - Full keyboard accessibility
- **Focus Management** - Proper focus indicators and management
- **Reduced Motion** - Respects user's motion preferences

### 📱 **Mobile Experience**

- **Responsive Navigation** - Hamburger menu with smooth animations
- **Touch Optimization** - Touch-friendly interactions and sizing
- **Viewport Handling** - Proper mobile viewport and safe areas
- **Progressive Enhancement** - Works on all devices and browsers

## 🔧 Development Tools

### 📊 **Performance Monitoring (Development Only)**

The project includes comprehensive performance monitoring that's automatically removed in production:

#### Visual Dashboard

- Click the 📊 button in the top-right corner
- View real-time performance metrics
- Monitor page load times, component render times, and memory usage

#### Console Monitoring

- Open browser DevTools (F12) → Console tab
- See real-time performance logs while navigating
- Component render times and Web Vitals tracking

#### Performance Testing

- Interactive test component on the Home page (development only)
- Intentionally triggers performance warnings for testing

### 🎨 **Styling Architecture**

- **CSS Modules** - Component-scoped styling
- **Global Utilities** - Responsive and animation utilities
- **Mobile-First** - Responsive design starting from mobile
- **CSS Custom Properties** - Consistent theming and spacing

## 🚀 Deployment

### Automated Deployment (Recommended)

**One-time setup:**

```bash
npm run deploy:setup
# Edit deploy.config with your S3 bucket name
```

**Deploy to production:**

```bash
npm run deploy
```

The automated script handles:

- ✅ Production build creation
- ✅ S3 bucket configuration
- ✅ File deployment with optimal caching
- ✅ Static website hosting setup
- ✅ Bucket policy configuration
- ✅ Deployment verification
- ✅ CloudFront invalidation (optional)

### Manual Deployment

```bash
npm run build:production
aws s3 sync build/ s3://your-bucket-name --delete
aws s3 website s3://your-bucket-name --index-document index.html --error-document index.html
```

See [DEPLOYMENT.md](./DEPLOYMENT.md) for detailed deployment instructions and troubleshooting.

## 🧪 Testing

### Manual Testing Checklist

- [ ] All pages load without errors
- [ ] Navigation works on desktop and mobile
- [ ] Course cards display correctly
- [ ] Course detail pages load with proper data
- [ ] Mobile menu functions properly
- [ ] Performance monitoring works in development
- [ ] No monitoring tools visible in production build

### Performance Testing

- Use the performance dashboard in development
- Check console for performance warnings
- Test on different devices and network conditions
- Verify Web Vitals meet targets

## 📈 Performance Targets

- **Component Render Time:** < 16ms (60fps)
- **First Byte (TTFB):** < 400ms
- **DOM Content Loaded:** < 1.5s
- **Load Complete:** < 3s
- **Largest Contentful Paint:** < 2.5s

## 🔒 Production Safety

All development tools are automatically removed in production builds:

- Performance monitoring dashboard
- Console performance logs
- Development test components
- Debug utilities

The production build is optimized and customer-ready for deployment.

## 🤝 Contributing

1. Follow the existing code structure and patterns
2. Use TypeScript for all new components
3. Add performance monitoring to new pages
4. Test both development and production builds
5. Ensure accessibility standards are met
6. Update documentation for new features

## 🛠️ Development Workflow

### Adding a New Page

1. Create page component in `src/pages/NewPage/`
2. Add TypeScript types if needed
3. Add performance monitoring with `usePerformance`
4. Add route to `src/constants/index.ts`
5. Update `App.tsx` with lazy-loaded route
6. Add navigation link to header if needed

### Adding a New Component

1. Create component directory in appropriate location
2. Include TypeScript interface for props
3. Add CSS module for styling
4. Export from index.ts file
5. Add to Storybook if applicable

### Performance Monitoring Setup

```typescript
import { usePerformance } from "../hooks";

const MyComponent = () => {
  usePerformance({
    componentName: "MyComponent",
    threshold: 20, // ms
  });

  return <div>My component</div>;
};
```

## 🐛 Troubleshooting

### Common Issues

**Build Fails with TypeScript Errors**

```bash
# Clear TypeScript cache
rm -rf node_modules/.cache
npm install
```

**Performance Dashboard Not Showing**

- Ensure you're in development mode (`npm start`)
- Check that `NODE_ENV !== 'production'`
- Look for the 📊 button in the top-right corner

**Mobile Menu Not Working**

- Check viewport meta tag in public/index.html
- Verify CSS media queries are loading
- Test on actual mobile device, not just browser resize

**Course Data Not Loading**

- Check browser console for errors
- Verify `src/data/courseData.ts` is properly formatted
- Ensure `useCourses` hook is imported correctly

**Production Build Missing Features**

- This is expected! Development tools are removed in production
- Use `npm start` for development with all features
- Use `npm run build:production` for clean production build

### Debug Mode

Enable additional logging in development:

```typescript
// In any component
console.log("Debug info:", { data, state });
```

## 📚 Learning Resources

### Technologies Used

- [React 18 Documentation](https://react.dev/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [React Router v6](https://reactrouter.com/)
- [CSS Modules](https://github.com/css-modules/css-modules)

### Performance Resources

- [Web Vitals](https://web.dev/vitals/)
- [React Performance](https://react.dev/learn/render-and-commit)
- [Lighthouse](https://developers.google.com/web/tools/lighthouse)

## 🔄 Version History

### Current Version: 1.0.0

- ✅ Full TypeScript conversion
- ✅ Performance monitoring system
- ✅ Mobile-responsive design
- ✅ Accessibility improvements
- ✅ Production-ready deployment
- ✅ Comprehensive error handling
- ✅ Lazy loading and optimization

### Planned Features

- [ ] Unit test suite with Jest
- [ ] E2E testing with Cypress
- [ ] Storybook component documentation
- [ ] PWA features (service worker, offline support)
- [ ] Advanced analytics integration
- [ ] A/B testing framework

## 📞 Support

For technical questions or issues:

1. Check this README and DEPLOYMENT.md
2. Review the troubleshooting section
3. Check browser console for errors
4. Verify all dependencies are installed correctly

## 📝 License

This project is private and proprietary to Bit By Bit.
