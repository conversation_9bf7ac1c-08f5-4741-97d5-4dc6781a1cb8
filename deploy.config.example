# Bit By Bit Deployment Configuration
# Copy this file to deploy.config and update with your values

# Required: Your S3 bucket name
BUCKET_NAME=your-bucket-name-here

# Optional: AWS region (default: us-east-1)
REGION=us-east-1

# HTTPS Configuration
# Set to true to enable HTTPS via CloudFront (recommended for production)
# Set to false to stay within AWS free tier (S3 only)
SETUP_HTTPS=false

# Optional: Your custom domain name (for HTTPS with custom domain)
# Leave empty to use CloudFront's default domain with free SSL
DOMAIN_NAME=

# Optional: Existing SSL certificate ARN (auto-generated if not provided)
CERTIFICATE_ARN=

# Optional: Existing CloudFront distribution ID (auto-created if not provided)
CLOUDFRONT_DISTRIBUTION_ID=

# Performance Options
# Skip waiting for CloudFront deployment (use S3 URL immediately)
SKIP_CLOUDFRONT_WAIT=false

# Wait for cache invalidation to complete (slower but ensures fresh content)
WAIT_FOR_INVALIDATION=false

# Bandwidth Optimization
# Skip upload if no files have changed (saves bandwidth and time)
SKIP_UNCHANGED_FILES=true

# Show detailed file change information during deployment
SHOW_FILE_CHANGES=true

# Example configurations:

# AWS Free Tier (recommended for low volume):
# BUCKET_NAME=bitbybit-website
# REGION=us-east-1
# SETUP_HTTPS=false
# Result: Free S3 static website (HTTP only)

# Basic HTTPS (uses CloudFront - not free tier):
# BUCKET_NAME=bitbybit-website-prod
# SETUP_HTTPS=true
# Result: HTTPS with CloudFront (costs apply)

# Custom domain with HTTPS (not free tier):
# BUCKET_NAME=bitbybit-website-prod
# DOMAIN_NAME=bitbybit.com
# SETUP_HTTPS=true
# Result: Custom domain with HTTPS (costs apply)
