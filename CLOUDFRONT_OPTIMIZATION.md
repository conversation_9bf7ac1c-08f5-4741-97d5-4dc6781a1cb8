# CloudFront Optimization Guide

## 🚀 Speeding Up CloudFront Deployment

### **The Reality: CloudFront Takes Time**
- **Initial deployment:** 15-20 minutes (unavoidable)
- **Cache invalidation:** 1-3 minutes (can be optimized)
- **Global propagation:** 5-15 minutes (automatic)

### **⚡ Fast Deployment Strategies**

## **Strategy 1: Use S3 Website URL Immediately**

**Instant Access (HTTP):**
```
http://your-bucket-name.s3-website-region.amazonaws.com
```

**Benefits:**
- ✅ **Instant availability** - No waiting for CloudFront
- ✅ **Perfect for testing** - Verify deployment immediately
- ✅ **Development workflow** - Fast iteration cycles

**Limitations:**
- ❌ **HTTP only** - No SSL encryption
- ❌ **No global CDN** - Slower for international users
- ❌ **No custom domain** - Uses AWS domain

## **Strategy 2: Optimize CloudFront Settings**

**Enhanced Deploy Script Features:**
```bash
# Check existing CloudFront status
check_cloudfront_status()

# Smart invalidation with tracking
invalidate_cloudfront()

# Fast deployment options
fast_deploy_option()
```

**Configuration Options:**
```bash
# Skip waiting for CloudFront (use S3 immediately)
SKIP_CLOUDFRONT_WAIT=true

# Wait for invalidation completion (ensures fresh content)
WAIT_FOR_INVALIDATION=true
```

## **Strategy 3: Deployment Workflow Optimization**

### **Development Workflow:**
```bash
# 1. Fast development deployments (S3 only)
SETUP_HTTPS=false npm run deploy

# 2. Production deployments (with CloudFront)
SETUP_HTTPS=true npm run deploy
```

### **Content Update Workflow:**
```bash
# 1. Deploy to S3 (instant)
npm run deploy

# 2. Test on S3 URL immediately
curl http://bucket.s3-website-region.amazonaws.com

# 3. Invalidate CloudFront (1-3 minutes)
# Automatic via deploy script

# 4. Verify CloudFront (after invalidation)
curl https://cloudfront-domain.com
```

## **🔄 Cache Invalidation Best Practices**

### **What Gets Invalidated:**
```bash
# Current script invalidates everything
--paths "/*"
```

### **Selective Invalidation (Faster & Cheaper):**
```bash
# Only invalidate changed files
--paths "/index.html" "/static/css/*" "/static/js/*"

# Specific files only
--paths "/index.html" "/manifest.json"
```

### **Cache Headers Optimization:**
```bash
# Static assets (long cache)
Cache-Control: public, max-age=31536000  # 1 year

# HTML files (no cache)
Cache-Control: no-cache, no-store, must-revalidate

# API responses (short cache)
Cache-Control: public, max-age=300  # 5 minutes
```

## **⚙️ CloudFront Configuration Optimizations**

### **Current Settings (Optimized):**
```json
{
  "DefaultTTL": 86400,        // 24 hours
  "MaxTTL": 31536000,         // 1 year
  "MinTTL": 0,                // No minimum
  "Compress": true,           // Gzip compression
  "ViewerProtocolPolicy": "redirect-to-https"
}
```

### **Performance Features:**
- ✅ **Gzip compression** - Smaller file sizes
- ✅ **HTTP/2 support** - Faster loading
- ✅ **Global edge locations** - Worldwide performance
- ✅ **Automatic HTTPS redirect** - Security + performance

## **📊 Deployment Time Comparison**

### **S3 Only (HTTP):**
- **Deployment time:** 30-60 seconds
- **Availability:** Immediate
- **Global performance:** Regional only
- **Security:** HTTP only

### **CloudFront + S3 (HTTPS):**
- **Initial deployment:** 15-20 minutes
- **Subsequent deployments:** 1-3 minutes (invalidation)
- **Availability:** Global CDN
- **Security:** HTTPS with SSL

### **Hybrid Approach (Recommended):**
1. **Deploy to S3** - Test immediately on HTTP
2. **CloudFront deploys in background** - No waiting
3. **Switch to HTTPS** - When CloudFront is ready
4. **Future updates** - Fast invalidation only

## **🛠️ Advanced Optimization Techniques**

### **1. Multiple Distributions:**
```bash
# Development distribution (faster settings)
CLOUDFRONT_DEV_ID=E123456789

# Production distribution (optimized for performance)
CLOUDFRONT_PROD_ID=E987654321
```

### **2. Origin Shield:**
```bash
# Add origin shield for better cache hit ratio
"OriginShield": {
    "Enabled": true,
    "OriginShieldRegion": "us-east-1"
}
```

### **3. Custom Cache Behaviors:**
```bash
# Different caching for different file types
"/api/*": { "TTL": 300 }      # 5 minutes
"/static/*": { "TTL": 31536000 }  # 1 year
"/*.html": { "TTL": 0 }       # No cache
```

## **🚀 Quick Commands**

### **Fast Development Deploy:**
```bash
# Skip CloudFront, use S3 only
echo "SETUP_HTTPS=false" >> deploy.config
npm run deploy
```

### **Production Deploy with Monitoring:**
```bash
# Full HTTPS deployment with status checking
echo "WAIT_FOR_INVALIDATION=true" >> deploy.config
npm run deploy
```

### **Manual CloudFront Operations:**
```bash
# Check distribution status
aws cloudfront get-distribution --id E123456789

# Create invalidation
aws cloudfront create-invalidation --distribution-id E123456789 --paths "/*"

# List invalidations
aws cloudfront list-invalidations --distribution-id E123456789
```

## **📈 Performance Monitoring**

### **Test CloudFront Performance:**
```bash
# Test global performance
curl -w "@curl-format.txt" -o /dev/null -s https://your-domain.com

# Check cache headers
curl -I https://your-domain.com

# Test from different regions
# Use online tools like GTmetrix, Pingdom, WebPageTest
```

### **Cache Hit Ratio Monitoring:**
- **CloudWatch metrics** - Monitor cache performance
- **Real User Monitoring** - Track actual user experience
- **Synthetic testing** - Automated performance checks

## **💡 Pro Tips**

### **1. Development Workflow:**
- Use S3 URLs for rapid development iteration
- Only use CloudFront for staging/production testing
- Set up separate distributions for dev/staging/prod

### **2. Content Strategy:**
- Version your static assets (`app.v123.js`)
- Use long cache times for versioned assets
- Keep HTML files with short/no cache

### **3. Deployment Strategy:**
- Deploy during low-traffic periods
- Use blue-green deployments for zero downtime
- Monitor cache hit ratios and adjust TTLs

### **4. Cost Optimization:**
- Use selective invalidation (cheaper than `/*`)
- Monitor data transfer costs
- Consider regional distributions for specific markets

**Your CloudFront deployment is now optimized for both speed and performance!** 🚀✨
